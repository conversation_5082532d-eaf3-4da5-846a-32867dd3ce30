"use client";

import { BackButton, TimerDisplay } from "@repo/core/exercise/components";

import { StudyType } from "@repo/core/enums";
import {
  ProgressBar,
  ProgressDisplayMode,
} from "@repo/core/exercise/components/ProgressBar";
import { NextQuestionInfo, StudentAnswer } from "@repo/core/exercise/model";
import {
  PreviewExerciseCbParams,
  PreviewQuestionData,
} from "@repo/core/exercise/model/types";
import { StudyTypeThemeProvider } from "@repo/core/exercise/theme";
import Button from "@repo/ui/components/press-button";
import { cn } from "@repo/ui/lib/utils";

import React, { useCallback, useEffect, useMemo } from "react";
import {
  QuestionPreviewContextProvider,
  useQuestionPreviewContext,
} from "../contexts/question-preview-context";
import {
  useCurrentQuestion,
  usePreviewViewModel,
} from "../viewmodel/preview-viewmodel";
import { QuestionView } from "./question-view";

function ExerciseViewContent({
  onBack,
  studyType,
  className,
  headerRight,
}: {
  onBack?: () => void;
  studyType: StudyType;
  className?: string;
  headerRight?: (questionId: string) => React.ReactElement;
}) {
  const questionContext = useQuestionPreviewContext();
  // 🎯 从Context获取题目相关状态
  const { timerControl } = useQuestionPreviewContext();

  const handleBack = useCallback(() => {
    onBack?.();
  }, [onBack]);

  return (
    <>
      <div
        className={cn(
          "exercise-page relative flex h-screen flex-col",
          className
        )}
        style={{ backgroundColor: "var(--study-background)" }}
      >
        <div className="pt-8.5 relative flex px-8 py-1">
          <div className="flex h-10 items-center">
            <BackButton onClick={handleBack} className="h-6" />
            <TimerDisplay
              timerControl={timerControl}
              className="ml-1 leading-6"
              // 预览模式：显示固定用时
              previewMode={true}
              previewDurationMs={questionContext.previewData?.answerDuration}
            />
          </div>
          {/* 进度条绝对居中定位 */}
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 translate-y-1/2">
            <ProgressBar
              activeType="static"
              progress={
                // 🔥 计算真实进度：(当前索引 + 1) / 总数 * 100
                questionContext.currentIndex !== undefined &&
                questionContext.totalCount
                  ? Math.round(
                      ((questionContext.currentIndex + 1) /
                        questionContext.totalCount) *
                        100
                    )
                  : 0
              }
              isPreviewMode={true}
              currentIndex={questionContext.currentIndex}
              totalCount={questionContext.totalCount}
              displayMode={questionContext.progressDisplayMode}
              className="progress-bar-preview"
            />
          </div>
          <div className="relative ml-auto flex justify-end">
            {headerRight &&
              headerRight(questionContext.currentQuestion?.questionId || "")}
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <QuestionView />
        </div>
      </div>
    </>
  );
}

interface ExerciseViewProps {
  showExplanations?: boolean;
  className?: string;
  // 🔥 新增：支持传入题目列表模式
  questionList?: PreviewQuestionData[];
  initialIndex?: number;

  // 🔥 新增：支持根据题目ID定位
  questionId?: string;

  // 可选参数（单题模式时使用）
  questionData?: NextQuestionInfo;
  studyType?: StudyType;

  onBack?: () => void;

  // 🔥 新增：进度显示模式控制
  progressDisplayMode?: ProgressDisplayMode; // 进度显示模式：'bar' 显示进度条，'number' 显示数字（默认为 'number'）

  // 🔥 新增：错题本操作回调函数（合并为toggle方法）
  onToggleWrongQuestionBook?: (
    questionId: string,
    isCurrentlyInBook: boolean
  ) => void | Promise<void>;
  // 🔥 新增：判断题目是否在错题本中的函数
  isInWrongQuestionBook?: (questionId: string) => boolean;

  // 预览数据（包含用时、答案统计等）
  previewData?: {
    answerDuration?: number; // 答题用时（毫秒）
    answerResult?: number; // 答题结果
    studentAnswer?: StudentAnswer; // 学生答题记录
  };

  // 继续按钮回调
  onContinue?: () => void;

  // 右侧按钮
  headerRight?: (questionId: string) => React.ReactElement;

  // 题目索引变化回调
  onQuestionIndexChange?: (index: number) => void;

  // 🔥 修改：使用对象参数的按钮控制函数
  showContinueButton?: boolean | ((params: PreviewExerciseCbParams) => boolean);
  /* 自定义CSS变量 */
  customVariables?: Record<string, string>;
}

export function ExerciseView({
  headerRight,
  showExplanations = true,
  className,
  questionList,
  initialIndex = 0,
  questionId,
  questionData,
  onBack,
  studyType: propStudyType,
  onToggleWrongQuestionBook,
  isInWrongQuestionBook,
  previewData,
  onContinue,
  onQuestionIndexChange,
  showContinueButton = ({ currentIndex, total }) =>
    currentIndex === total - 1 || total === 1,
  progressDisplayMode = "number", // 🔥 默认显示数字模式
  customVariables,
}: ExerciseViewProps) {
  const studyType = propStudyType || StudyType.REINFORCEMENT_EXERCISE;

  // 🔥 题目列表模式：使用 ViewModel 管理列表和导航
  const questionInfoList = useMemo(
    () =>
      questionList?.map((item) => ({
        ...item.questionInfo,
        progressInfo: item.questionInfo.progressInfo || {
          totalQuestions: questionList.length,
          completedQuestions: 0,
          currentProgress: 0,
        },
        hasNextQuestion: item.questionInfo.hasNextQuestion ?? null,
      })) || [],
    [questionList]
  );

  // 🔥 根据 questionId 查找对应的索引位置
  const resolvedInitialIndex = useMemo(() => {
    if (!questionId || !questionList) {
      return initialIndex;
    }

    const foundIndex = questionList.findIndex(
      (item) => item.questionInfo.questionId === questionId
    );

    if (foundIndex === -1) {
      console.warn(
        `[ExerciseView] 未找到 questionId: ${questionId}，使用默认索引 ${initialIndex}`
      );
      return initialIndex;
    }

    console.log(
      `[ExerciseView] 根据 questionId: ${questionId} 定位到索引: ${foundIndex}`
    );
    return foundIndex;
  }, [questionId, questionList, initialIndex]);

  const previewViewModel = usePreviewViewModel(
    questionInfoList,
    resolvedInitialIndex
  );
  const currentQuestionInfo = useCurrentQuestion(previewViewModel);

  useEffect(() => {
    onQuestionIndexChange?.(previewViewModel.currentIndex);
  }, [previewViewModel.currentIndex]);
  const exerciseCbParams: PreviewExerciseCbParams = useMemo(() => {
    return {
      currentIndex: previewViewModel.currentIndex,
      total: questionInfoList.length,
      isLastQuestion:
        previewViewModel.currentIndex === questionInfoList.length - 1,
      isFirstQuestion: previewViewModel.currentIndex === 0,
      progress: Math.round(
        ((previewViewModel.currentIndex + 1) / questionInfoList.length) * 100
      ),
      questionId: currentQuestionInfo?.questionId,
      questionType: currentQuestionInfo?.questionType ?? undefined,
    };
  }, [previewViewModel.currentIndex, questionInfoList.length]);

  // 🔥 获取当前题目的完整预览数据
  const currentPreviewData = useMemo(() => {
    if (!questionList) return previewData;

    const currentData = questionList[previewViewModel.currentIndex];
    if (!currentData?.studentAnswer) return undefined;

    return {
      answerDuration: currentData.studentAnswer.answerDuration,
      answerResult: currentData.studentAnswer.answerResult,
      studentAnswer: currentData.studentAnswer,
    };
  }, [questionList, previewViewModel.currentIndex, previewData]);

  // 🔥 构建操作按钮（仅在预览模式且有题目列表时显示）
  const actionButtons = useMemo(() => {
    // if (!questionList) return null;

    // 默认显示解析
    if (!previewViewModel.showExplanations) {
      previewViewModel.toggleShowExplanations();
    }

    // TODO: 等后续错题本上线了以后再打开 - 获取当前题目ID
    // const currentQuestionId = currentQuestionInfo?.questionId;

    // TODO: 等后续错题本上线了以后再打开 - 判断当前题目是否在错题本中
    // const isCurrentInWrongBook = currentQuestionId && isInWrongQuestionBook ?
    //   isInWrongQuestionBook(currentQuestionId) : false;

    // TODO: 等后续错题本上线了以后再打开 - 错题本操作处理函数
    // const handleWrongQuestionBookAction = async () => {
    //   if (!currentQuestionId) return;
    //
    //   try {
    //     // 使用 toggle 方法，传入当前状态
    //     await onToggleWrongQuestionBook?.(currentQuestionId, isCurrentInWrongBook);
    //   } catch (error) {
    //     console.error('错题本操作失败:', error);
    //   }
    // };

    const isShowContinueButton =
      typeof showContinueButton === "function"
        ? showContinueButton(exerciseCbParams)
        : showContinueButton;
    return (
      <div className="preview-action-buttons flex items-center gap-2">
        {previewViewModel.canGoPrevious && (
          <Button
            size="lg"
            color="white"
            onClick={previewViewModel.goToPrevious}
          >
            上一题
          </Button>
        )}
        {previewViewModel.canGoNext && (
          <Button size="lg" color="white" onClick={previewViewModel.goToNext}>
            下一题
          </Button>
        )}
        {/* TODO: 等后续错题本上线了以后再打开 - 只有提供了错题本操作回调函数时才显示按钮 */}
        {/* {onToggleWrongQuestionBook && (
          <Button
            color={isCurrentInWrongBook ? 'gray' : 'orange'}
            onClick={handleWrongQuestionBookAction}
          >
            {isCurrentInWrongBook ? '移出错题本' : '加入错题本'}
          </Button>
        )} */}
        {/* 继续按钮 */}
        {(() => {
          // 🔥 使用对象参数的 showContinueButton 函数控制按钮显示
          const shouldShow = isShowContinueButton;

          return (
            shouldShow && (
              <Button
                size="lg"
                color="study-theme"
                studyType={studyType}
                onClick={onContinue}
              >
                继续
              </Button>
            )
          );
        })()}
      </div>
    );
  }, [
    studyType,
    exerciseCbParams,
    onContinue,
    showContinueButton,
    previewViewModel,
  ]);

  // 🔥 确定当前显示的题目
  const currentQuestion = useMemo(() => {
    // 题目列表模式
    if (questionList && currentQuestionInfo) {
      try {
        return currentQuestionInfo;
      } catch (error) {
        console.error("[ExerciseView] 题目转换失败:", error);
        return null;
      }
    }

    // 单题模式
    return questionData || null;
  }, [questionList, currentQuestionInfo, questionData]);

  // 🔥 错误状态处理
  if (questionList && !currentQuestion) {
    return (
      <div className="exercise-error-container flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2 text-lg font-medium text-red-600">
            题目数据错误
          </div>
          <div className="text-sm text-gray-400">
            无法解析题目数据或题目列表为空
          </div>
        </div>
      </div>
    );
  }

  // 🔥 加载状态处理
  if (!currentQuestion) {
    return (
      <div className="exercise-loading-container flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2 text-lg font-medium text-gray-600">
            加载中...
          </div>
          <div className="text-sm text-gray-400">正在获取题目数据</div>
        </div>
      </div>
    );
  }

  return (
    <StudyTypeThemeProvider
      studyType={studyType}
      className="h-full"
      customVariables={customVariables}
    >
      <QuestionPreviewContextProvider
        showExplanations={showExplanations}
        currentQuestion={currentQuestion}
        actionButtons={actionButtons}
        currentIndex={questionList ? previewViewModel.currentIndex : undefined}
        totalCount={
          questionList ? previewViewModel.questionList.length : undefined
        }
        progressDisplayMode={progressDisplayMode}
        previewData={currentPreviewData}
      >
        <ExerciseViewContent
          headerRight={headerRight}
          onBack={onBack}
          studyType={studyType}
          className={className}
        />
      </QuestionPreviewContextProvider>
    </StudyTypeThemeProvider>
  );
}

export const ExercisePreview = ExerciseView;

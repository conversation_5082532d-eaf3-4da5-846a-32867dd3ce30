import MdPreview from "../MdPreview";
import { QaContentType } from "../type";
import QuestionOptionsDisplay from "./QuestionOptions";
import { SubQuestionList } from "./SubQuestionList";

export function QuestionContent({
  qaContent,
  ossHost,
  levels,
}: {
  qaContent: QaContentType;
  ossHost?: string;
  levels?: (string | number)[];
}) {
  if (qaContent.questionType === 7) {
    return (
      <SubQuestionList
        qaContent={qaContent}
        ossHost={ossHost}
        levels={levels}
      />
    );
  }

  return (
    <div style={{ lineHeight: "2.5" }}>
      <MdPreview
        content={qaContent.content}
        ossHost={ossHost}
        questionId={qaContent.questionId}
      />
      <QuestionOptionsDisplay qaContent={qaContent} ossHost={ossHost} />
    </div>
  );
}

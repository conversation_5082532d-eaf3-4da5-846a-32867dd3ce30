import { useMemo } from "react";
import MdPreview from "../../MdPreview";
import { QaContentType } from "../../type";

const keyMode = [1, 2, 4];
const valMode = [3, 5];

function getAnswer(content: QaContentType) {
  const {
    questionAnswer: { answerOptionList },
    questionAnswerMode,
  } = content;
  const isKeyMode = questionAnswerMode && keyMode.includes(questionAnswerMode);
  const isValMode = questionAnswerMode && valMode.includes(questionAnswerMode);

  if (answerOptionList && answerOptionList.length > 0) {
    return answerOptionList
      .map((item) => {
        if (isKeyMode) {
          return `${String(item.optionKey)}`.trim();
        }
        if (isValMode) {
          return `${String(item.optionVal)}`.trim();
        }
        return [String(item.optionKey), String(item.optionVal)]
          .filter(Boolean)
          .join(" ");
      })
      .join(isKeyMode ? "" : "<br/>");
  }

  return "";
}

type AnswerAndExplainItem = {
  level: string; //number[];
  explain: string;
  answer: string;
};

function walk(content: QaContentType, list: AnswerAndExplainItem[]) {
  if (content.questionType === 7) {
    // content.subQuestionList?.forEach((question, index) =>
    //   walk(question, [...level, index + 1], list)
    // );
    content.subQuestionAnswers?.forEach((question) =>
      list.push({
        level: question.questionIndexStr || "",
        explain: question.answerExplain,
        answer: getAnswer(question),
      })
    );
  } else {
    list.push({
      level: "",
      explain: content.answerExplain,
      answer: getAnswer(content),
    });
  }
}

export function AnswerAndExplain({
  qaContent,
  ossHost,
}: {
  qaContent: QaContentType;
  ossHost?: string;
}) {
  const contentList = useMemo(() => {
    const list: AnswerAndExplainItem[] = [];

    walk(qaContent, list);

    return list;
  }, [qaContent]);

  return (
    <div className="flex flex-col gap-2">
      {contentList.map(({ level, explain, answer }) => (
        <div key={level} className="flex gap-2">
          {level && (
            <div
              className="mt-2 flex-shrink-0 whitespace-nowrap font-medium"
              style={{ minWidth: "2.5rem" }}
            >
              {level}
            </div>
          )}
          <div className="flex flex-col gap-2">
            <div className="flex items-start gap-2">
              <p className="text-gray-2 mt-2 flex-shrink-0 whitespace-nowrap font-medium">
                答案：
              </p>
              <MdPreview
                content={answer}
                ossHost={ossHost}
                questionId={qaContent.questionId}
              />
            </div>
            {explain && (
              <div className="flex items-start gap-2">
                <p className="text-gray-2 mt-2 flex-shrink-0 whitespace-nowrap font-medium">
                  解析：
                </p>
                <div className="max-w-full overflow-hidden">
                  <MdPreview
                    content={explain}
                    ossHost={ossHost}
                    questionId={qaContent.questionId}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

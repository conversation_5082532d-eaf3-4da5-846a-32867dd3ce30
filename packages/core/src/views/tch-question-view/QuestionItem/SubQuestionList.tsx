import MdPreview from "../MdPreview";
import { QaContentType } from "../type";

export function SubQuestionList({
  qaContent,
  ossHost,
  levels,
}: {
  qaContent: QaContentType;
  ossHost?: string;
  levels?: (string | number)[];
}) {
  return (
    <>
      <MdPreview
        content={qaContent.content}
        ossHost={ossHost}
        questionId={qaContent.questionId}
      />
      <div className="flex flex-col gap-4">
        {qaContent.subQuestionList?.map((question, index) => (
          <div
            key={question.questionId}
            className="border-line-3 flex items-start gap-[0.5rem] border-t border-dashed"
          >
            {/* TODO：这个可能需要根据Level做优化，现在只是简单处理了，需要等设计确认最终稿 */}
            <div className="line-height-1 mt-1 flex min-w-8 shrink-0 items-center justify-center rounded-2xl p-1">
              <h3 className="text-gray-2 font-medium">{index + 1}.</h3>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}

import { Picture, Subtitle, Video } from "./base";

export enum GuideMode {
  free = "free",
  follow = "follow",
}

// 标记
interface RoughNotation {
  // 类型: 圆圈, 括号, 方框, 下划线, 删除线
  // 默认: 下划线
  // 现在支持: underline, circle, highlight
  type:
    | "underline"
    | "box"
    | "circle"
    | "highlight"
    | "strike-through"
    | "crossed-off"
    | "bracket";
  // 颜色
  color?: string;
  // 宽度
  strokeWidth?: number;
  // 开始帧
  inFrame?: number;
  // 绘制时间(帧)
  duration?: number;
  // 结束帧
  outFrame?: number;
}

// 行文本
interface LineTexture {
  // 类型: 默认, 加粗
  tag: "normal" | "bold";
  // 内容
  content: string;
  // 开始帧
  inFrame?: number;
  // 结束帧
  outFrame?: number;
  // 标记
  notation?: RoughNotation;
}

// 画布类型
interface DrawElement {
  id?: string;
  drawMode: boolean;
  startTimestamp: number;
  endTimestamp: number;
  inFrame: number;
  outFrame: number;
  paths: {
    x: number;
    y: number;
  }[];
  strokeColor: string;
  strokeWidth: number;
  endFrame?: number;
}

// 行
interface Line {
  // 唯一标识
  id?: string;
  // 行内容计算出来的hash
  lineId?: string;
  // 类型: 默认, 有序列表, 无序列表, 标题, 图片
  tag: "default" | "ol" | "ul" | "h1" | "h2" | "h3" | "h4" | "block";
  // 层级: 1, 2, 3, 4
  level: number;
  // 排序, 只对ol 有效
  order?: number;
  // 内容
  content: LineTexture[] | Line[];
  // 开始帧
  inFrame: number;
  // 结束帧
  outFrame: number;
  // 图片
  pic?: Picture;
  // 宽度
  width?: string;
  // 画布
  draw?: DrawElement[]; // 仅限h3级

  layout?: "vertical" | "horizontal"; // 布局方向
  imageRatio?: "16:9" | "1:1" | "9:16"; // 图片比例
  styleType?: "style1" | "style2" | "style3" | "style4" | "style5"; // 用户选择的样式类型
  styleText?: number; // 用户选择的样式类型
}

interface GuideWidgetData {
  // 数字人视频
  avatar: Video;
  // 字幕
  subtitles: Subtitle[];
  // 标题
  title: string;
  // 内容
  content: Line[];

  // 缓存过本地的音频地址
  blobUrl?: string;
}

interface GuideTheme {
  titleBg?: string;
  // 插画
  lastPlate?: string;
  // 封底
  backCover?: string;
}

export type {
  DrawElement,
  GuideTheme,
  GuideWidgetData,
  Line,
  LineTexture,
  RoughNotation,
  Subtitle
};


import { MergedReference, Reference } from "@repo/core/types/data/comment";
import {
  DrawElement,
  Line,
  LineTexture,
} from "@repo/core/types/data/widget-guide";
import {
  FC,
  ReactNode,
  RefObject,
  createContext,
  useContext,
  useMemo,
  useRef,
} from "react";
import { toH4Parts } from "../utils/h3-parts";
import { useGuideContext } from "./guide-context";

// type GuideH3PartContextType = GuideViewContextType & {
type SectionH3ContextType = {
  index: number;
  ref: RefObject<HTMLDivElement | null>;
  data: Line[];
  h3Line: Line | undefined;
  parts: Line[][];
  sketchProps?: {
    mode?: "draw" | "edit";
    changeLine?: (draw: string, id: string) => void;
    onDrawChange?: (paths: DrawElement[] | null) => void;
    startFrame?: number;
    outFrame?: number;
    duration?: number;
    eraserMode?: boolean;
    highlighter?: boolean;
  };
  parentStyleText?: number;
  highlighter?: boolean;
  hardIconNum?: number;
  lineIds: string[];
  mergedReferences: MergedReference;
  partFrames: {
    inFrame: number;
    outFrame: number;
  };
};

const SectionH3Context = createContext<SectionH3ContextType>(
  {} as SectionH3ContextType
);

const useSectionH3Context = () => useContext(SectionH3Context);

interface SectionH3ProviderProps {
  index: number;
  data: Line[];
  children: ReactNode;
  sketchProps?: {
    onDrawChange?: (paths: DrawElement[] | null) => void;
    mode?: "draw" | "edit";
    changeLine?: (draw: string, id: string) => void;
    startFrame?: number;
    outFrame?: number;
    duration?: number;
    eraserMode?: boolean;
    highlighter?: boolean;
  };
  parentStyleText?: number;
}

const SectionH3Provider: FC<SectionH3ProviderProps> = ({
  children,
  data,
  index,
  ...props
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const h3Line = useMemo(() => {
    const line = data[0];
    if (!line) return undefined;
    if (line.tag === "h3") return line;
    return undefined;
  }, [data]);

  const lineIds = useMemo(() => {
    const ids: string[] = [];
    const iterator = (line: Line | LineTexture) => {
      if (line.tag === "normal" || line.tag === "bold") return;
      const _line = line as Line;
      ids.push(_line.lineId!);
      _line.content.forEach(iterator);
    };
    data.forEach(iterator);
    return ids;
  }, [data]);

  const { referenceList } = useGuideContext();
  const filteredReferences = useMemo(() => {
    if (!referenceList) return [];
    return referenceList
      .filter((it) =>
        it.referencePosition.data.every((line) => lineIds.includes(line.lineId))
      )
      .sort((a, b) => {
        const pa = a.referencePosition.data.find((item) => item.type !== "pic");
        const pb = b.referencePosition.data.find((item) => item.type !== "pic");
        if (!pa) return 1;
        if (!pb) return -1;
        const lineIndexA = lineIds.indexOf(pa.lineId);
        const lineIndexB = lineIds.indexOf(pb.lineId);
        if (lineIndexA !== lineIndexB) {
          return lineIndexA - lineIndexB;
        }
        const textureIdA = pa.textureId;
        const textureIdB = pb.textureId;
        if (textureIdA !== textureIdB) {
          return Number(textureIdA) - Number(textureIdB);
        }
        return Number(pa.start) - Number(pb.start);
      });
  }, [lineIds, referenceList]);

  const mergedReferences = useMemo(() => {
    if (!filteredReferences.length) return {};
    const references: (Reference & {
      referenceIds: { [referenceId: string]: string };
    })[] = [];
    filteredReferences.forEach((reference) => {
      const last = references.at(-1);
      if (!last) {
        return references.push({
          ...reference,
          referenceIds: {
            [reference.referenceId]: reference.referencePositionMd5,
          },
        });
      }
      const lastPos = last.referencePosition.data;
      const firstPosOfCurrent = reference.referencePosition.data[0]!;
      if (
        !lastPos.find(
          (item) =>
            item.lineId === firstPosOfCurrent.lineId &&
            item.textureId === firstPosOfCurrent.textureId &&
            (item.type === "text"
              ? Number(item.end) >= Number(firstPosOfCurrent.start)
              : true)
        )
      ) {
        return references.push({
          ...reference,
          referenceIds: {
            [reference.referenceId]: reference.referencePositionMd5,
          },
        });
      }
      reference.referencePosition.data.forEach((item) => {
        const j = lastPos.find(
          (j) => item.lineId === j.lineId && item.textureId === j.textureId
        );
        if (!j) return lastPos.push(item);
        if (item.type === "text" && j.type === "text") {
          j.end = String(Math.max(Number(item.end), Number(j.end)));
        }
      });
      last.referenceIds[reference.referenceId] = reference.referencePositionMd5;
      last.commentCount += reference.commentCount;
      last.referenceType |= reference.referenceType;
      last.replyed = Math.max(reference.replyed, last.replyed);
    });

    const result: SectionH3ContextType["mergedReferences"] = {};
    references.forEach((reference) => {
      reference.referencePosition.data.forEach((item, index) => {
        if (!result[item.lineId]) result[item.lineId] = {};
        if (!result[item.lineId]![item.textureId || "pic"]) {
          result[item.lineId]![item.textureId || "pic"] = [];
        }
        const isLast = index === reference.referencePosition.data.length - 1;
        if (item.type === "pic") {
          return result[item.lineId]![item.textureId || "pic"]!.push({
            start: 0,
            end: 0,
            isLast,
            referenceIds: reference.referenceIds,
            referenceType: reference.referenceType,
            commentCount: reference.commentCount,
            referencePosition: reference.referencePosition.data,
            replyed: reference.replyed,
          });
        }
        result[item.lineId]![item.textureId!]!.push({
          start: Number(item.start),
          end: Number(item.end),
          isLast,
          referenceIds: reference.referenceIds,
          referenceType: reference.referenceType,
          commentCount: reference.commentCount,
          referencePosition: reference.referencePosition.data,
          replyed: reference.replyed,
        });
      });
    });

    return result;
  }, [filteredReferences]);

  const parts = useMemo(() => toH4Parts(data), [data]);
  const partFrames = useMemo(() => {
    if (!data) return { inFrame: 0, outFrame: 0 };

    const firstLine = data[0];
    const lastLine = data[data.length - 1];
    let inFrame = firstLine?.inFrame || 0;
    if (firstLine?.inFrame === undefined) {
      inFrame = data[1]?.inFrame || 0;
    }
    const outFrame = lastLine?.outFrame || 0;

    return {
      inFrame,
      outFrame,
    };
  }, [data]);

  const value = {
    index,
    ref,
    data,
    h3Line,
    parts,
    lineIds,
    mergedReferences,
    partFrames,
    ...props,
  };

  return <SectionH3Context value={value}>{children}</SectionH3Context>;
};

export {
  SectionH3Context,
  SectionH3Provider,
  useSectionH3Context,
  type SectionH3ContextType,
  type SectionH3ProviderProps,
};

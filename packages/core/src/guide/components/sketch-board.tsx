import { DrawElement } from "@repo/core/types/data/widget-guide";
import svgToBase64 from "@repo/core/utils/svgToBase64";
import {
  CanvasPath,
  ReactSketchCanvas,
  ReactSketchCanvasRef,
} from "@repo/react-sketch-canvas";
import { cn } from "@repo/ui/lib/utils";
import isEqual from "lodash/isEqual";
import { useEffect, useRef, useState } from "react";
import { useCurrentFrame } from "remotion";
import { useGuideContext } from "../context/guide-context";
import { useSketchCanvasRef } from "../context/sketch-canvas-context";

// Douglas-Peucker 抽稀算法实现
const douglasPeucker = (
  points: { x: number; y: number }[],
  epsilon: number
): { x: number; y: number }[] => {
  if (points.length <= 2) return points;

  // 找到距离最远的点
  let maxDistance = 0;
  let maxIndex = 0;
  const start = points[0];
  const end = points[points.length - 1];

  // 确保start和end存在
  if (!start || !end) return points;

  for (let i = 1; i < points.length - 1; i++) {
    const point = points[i];
    if (point) {
      const distance = perpendicularDistance(point, start, end);
      if (distance > maxDistance) {
        maxDistance = distance;
        maxIndex = i;
      }
    }
  }

  // 如果最大距离大于阈值，则递归处理
  if (maxDistance > epsilon) {
    const left = douglasPeucker(points.slice(0, maxIndex + 1), epsilon);
    const right = douglasPeucker(points.slice(maxIndex), epsilon);
    return [...left.slice(0, -1), ...right];
  } else {
    return [start, end];
  }
};

// 计算点到线段的垂直距离
const perpendicularDistance = (
  point: { x: number; y: number },
  lineStart: { x: number; y: number },
  lineEnd: { x: number; y: number }
): number => {
  const { x, y } = point;
  const { x: x1, y: y1 } = lineStart;
  const { x: x2, y: y2 } = lineEnd;

  if (x1 === x2 && y1 === y2) {
    return Math.sqrt((x - x1) ** 2 + (y - y1) ** 2);
  }

  const A = x - x1;
  const B = y - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;
  let param = -1;

  if (lenSq !== 0) param = dot / lenSq;

  let xx, yy;

  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  const dx = x - xx;
  const dy = y - yy;

  return Math.sqrt(dx * dx + dy * dy);
};

// 路径抽稀处理函数
const simplifyPath = (
  paths: { x: number; y: number }[],
  targetCount: number = 100
): { x: number; y: number }[] => {
  if (paths.length <= targetCount) return paths;

  // 根据点的数量动态调整epsilon值
  const epsilon = Math.max(0.5, (paths.length / targetCount) * 0.5);
  return douglasPeucker(paths, epsilon);
};

// 橡皮擦宽度倍数常量
const ERASER_WIDTH_MULTIPLIER = 3;

// 荧光笔完全显示后延迟2秒后开始淡出
const highlighterDelayTime = 2;
// 荧光笔淡出时间
const highlighterFadeoutTime = 0.3;
// 荧光笔延长两秒半显示时间，延迟2秒后0.3秒内淡出
const HIGHLIGHTER_EXTEND_TIME = highlighterDelayTime + highlighterFadeoutTime;

// 扩展 CanvasPath 类型
interface ExtendedCanvasPath extends CanvasPath {
  inFrame?: number;
  outFrame?: number;
  endFrame?: number;
  wait?: boolean;
  strokeColor: string;
  strokeWidth: number;
  drawMode: boolean;
  paths: { x: number; y: number }[];
}

interface SketchBoardProps {
  svgData?: string; // SVG 字符串作为初始内容
  readOnly?: boolean;
  onChange?: (paths: DrawElement[] | null) => void;
  className?: string;
  itemId: string;
  changeLine?: (draw: string, id: string) => void;
  ref?: React.RefObject<ReactSketchCanvasRef | null> | null;
  strokeColor?: string;
  strokeWidth?: number;
  eraserMode?: boolean;
  highlighter?: boolean;
  defaultPaths?: CanvasPath[];
  animated?: boolean;
  startFrame: number; // 偏移量
  outFrame: number; // 视频最大帧
  allPaths?: ExtendedCanvasPath[];
}

export const SketchBoard: React.FC<SketchBoardProps> = ({
  onChange,
  svgData,
  readOnly = false,
  className,
  ref,
  // strokeColor = "#000000",
  // strokeWidth = 4,
  eraserMode = false,
  defaultPaths = [],
  animated = false,
  allPaths,
  startFrame,
  outFrame,
  highlighter,
}) => {
  const { data } = useGuideContext();
  const { avatar } = data;
  const { fps } = avatar;

  const { strokeColor, strokeWidth } = useSketchCanvasRef();
  const localRef = useRef<ReactSketchCanvasRef>(null);
  const canvasRef = ref || localRef;

  const [svgDataUrl, setSvgDataUrl] = useState("");
  const frame = useCurrentFrame();
  const [currentStrokeStartFrame, setCurrentStrokeStartFrame] = useState<
    number | null
  >(null);
  const prevPathsRef = useRef<CanvasPath[] | undefined>(undefined);
  // 橡皮擦范围提示相关 state
  const [showEraser, setShowEraser] = useState(false);
  const eraserRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const svgDataurl = svgData ? svgToBase64(svgData) : "";
    setSvgDataUrl(svgDataurl);
  }, [svgData]);

  // 动画回放：根据frame动态裁剪allPaths
  const animatedPaths: ExtendedCanvasPath[] | undefined =
    animated && allPaths
      ? allPaths
          .filter((p) => {
            if (p.endFrame && !p.wait && p.inFrame !== undefined) {
              return frame < p.endFrame && frame >= p.inFrame;
            }

            return (
              typeof p.inFrame === "number" &&
              Math.max(frame, startFrame) >= p.inFrame
            );
          })
          .map((p) => {
            const isHighlighter = p.endFrame !== undefined;
            if (
              // 1、未播放状态画，一直显示
              (p.outFrame === startFrame && p.inFrame === startFrame) ||
              // 2、播放中画，当前帧超过最大帧后一直显示
              (!isHighlighter && frame >= (p.outFrame ?? frame))
            ) {
              return p;
            }
            const outFrame = p.outFrame ?? frame;
            const totalFrames = outFrame - (p.inFrame ?? startFrame) + 1;
            const currentFrames = Math.max(
              1,
              Math.max(frame, startFrame) - (p.inFrame ?? startFrame)
            );
            const pointCount = Math.floor(
              (p.paths?.length ? p.paths.length : 0) *
                (currentFrames / totalFrames)
            );
            return {
              ...p,
              paths: Array.isArray(p.paths) ? p.paths.slice(0, pointCount) : [],
              // 记录动画过程中的当前frame
              outFrame: frame,
            } as CanvasPath;
          })
      : undefined;
  useEffect(() => {
    if (animated && animatedPaths && canvasRef?.current) {
      if (!isEqual(prevPathsRef.current, animatedPaths)) {
        canvasRef.current.clearCanvas();
        canvasRef.current.loadPaths(animatedPaths);
        prevPathsRef.current = animatedPaths;
      }
    } else if (!animated && defaultPaths && canvasRef?.current) {
      if (!isEqual(prevPathsRef.current, defaultPaths)) {
        canvasRef.current.clearCanvas();
        canvasRef.current.loadPaths(defaultPaths);
        prevPathsRef.current = defaultPaths;
      }
    }
  }, [animated, animatedPaths, defaultPaths, canvasRef]);

  const handleStroke = (
    path: ExtendedCanvasPath & { endFrame?: number; wait?: boolean }
  ) => {
    const currentFrame = frame;

    if (currentStrokeStartFrame === null) {
      // 开始新的一笔
      setCurrentStrokeStartFrame(currentFrame);
      // 添加开始帧到path
      path.inFrame = currentFrame;
      onChange?.(null);
    } else {
      // 结束当前笔
      // 添加结束帧到path
      let curOutFrame = currentFrame;
      if (path.inFrame !== undefined) {
        path.outFrame = curOutFrame;

        // 如果是荧光笔，延长显示
        if (highlighter) {
          path.endFrame =
            path.outFrame + Math.floor(fps * HIGHLIGHTER_EXTEND_TIME);
          // 临时添加供前端交互使用，提交接口前会删除
          path.wait = true;
        }
      }
      // 数据抽稀
      path.paths = simplifyPath(path.paths);

      // 创建新的路径对象，避免直接修改原对象
      const newPath = { ...path };

      // 这里要保持现有结构不能变，每次新的path都要放到list最后
      const updatedPaths = [
        ...(allPaths || []),
        newPath,
      ] as unknown as DrawElement[];
      onChange?.(updatedPaths);
      setCurrentStrokeStartFrame(null);
    }
  };
  return (
    <div
      className={cn(
        "homework_sketchboard_container relative h-full w-full rounded border border-dashed border-stone-500",
        className,
        eraserMode ? "cursor-none" : ""
      )}
      onMouseMove={(e) => {
        if (!eraserMode) return;
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        if (eraserRef.current) {
          const offset = strokeWidth * 1.45;
          eraserRef.current.style.left = `${x - offset}px`;
          eraserRef.current.style.top = `${y - offset}px`;
        }
      }}
      onMouseEnter={() => {
        if (eraserMode) setShowEraser(true);
      }}
      onMouseLeave={() => {
        setShowEraser(false);
      }}
    >
      <ReactSketchCanvas
        ref={ref}
        width="100%"
        height="100%"
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        eraserWidth={strokeWidth * ERASER_WIDTH_MULTIPLIER}
        canvasColor="transparent"
        style={{
          border: "none",
          background: "transparent",
        }}
        backgroundImage={svgDataUrl}
        allowOnlyPointerType={readOnly ? "none" : "all"}
        withTimestamp={true}
        onStroke={handleStroke}
      />
      {eraserMode && showEraser && (
        <div
          ref={eraserRef}
          className="eraser_range pointer-events-none absolute"
          style={{
            left: 0,
            top: 0,
            width: strokeWidth * ERASER_WIDTH_MULTIPLIER,
            height: strokeWidth * ERASER_WIDTH_MULTIPLIER,
            borderRadius: "50%",
            border: "2px solid #6b7280",
            background: "#6b7280",
            zIndex: 10,
            pointerEvents: "none",
            display: showEraser ? "block" : "none",
          }}
        />
      )}
    </div>
  );
};

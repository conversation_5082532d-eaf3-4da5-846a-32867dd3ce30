import { Layer, LayerItem } from "@repo/core/components/layer";
import { Subtitle } from "@repo/core/guide/components/subtitle";
import { FC } from "react";
import { AbsoluteFill, OffthreadVideo } from "remotion";

import { useGuideContext } from "@repo/core/guide/context/guide-context";

// import { GuideSectionH3 } from "@repo/core/guide/components/guide-section-h3";
import { GuideSectionH2 } from "@repo/core/guide/components/guide-section-h2";
import { DrawElement } from "@repo/core/types/data/widget-guide";

interface GuideCoreProps {
  startFrame?: number;
  outFrame?: number;
  duration?: number;
  onDrawChange?: (paths: DrawElement[] | null) => void;
  eraserMode?: boolean;
  highlighter?: boolean;
}

export const GuideCore: FC<GuideCoreProps> = ({
  startFrame = 0,
  outFrame = 0,
  duration = 0,
  onDrawChange,
  eraserMode,
  highlighter,
}) => {
  const { data } = useGuideContext();

  const { avatar, subtitles } = data;

  return (
    <AbsoluteFill>
      <div className="guide-view relative flex h-full w-full bg-[#FFFDFA]">
        <Layer className="font-resource-han-rounded">
          <LayerItem index={2} className="pb-27 w-full py-8">
            <div className="relative flex h-full w-full flex-row overflow-y-scroll scroll-smooth">
              <div className="flex h-auto w-full flex-1 flex-col pt-1">
                <GuideSectionH2
                  sketchProps={{
                    onDrawChange,
                    eraserMode,
                    highlighter,
                    mode: "draw",
                    startFrame: startFrame,
                    outFrame: outFrame,
                    duration: duration,
                  }}
                />
                <div className="w-section-h3 relative ml-8 flex h-full flex-col gap-3">
                  {/* <GuideSectionH3
                    data={content}
                    hardIconNum={hardIconNum}
                    onDrawChange={onDrawChange}
                    eraserMode={eraserMode}
                    highlighter={highlighter}
                    sketchProps={{
                      mode: "draw",
                      // changeLine: changeLine,
                      startFrame: startFrame,
                      outFrame: outFrame,
                      duration: duration,
                    }}
                  /> */}
                </div>
              </div>
              {/* <div className="w-1/4 bg-transparent">&nbsp;</div> */}
            </div>
          </LayerItem>
          <LayerItem index={1} className="right-0 w-1/5">
            <div className="relative flex h-full w-full flex-col items-center justify-end text-blue-800">
              {avatar.url && (
                <OffthreadVideo
                  src={avatar.url}
                  pauseWhenBuffering
                  crossOrigin="anonymous"
                />
              )}
            </div>
          </LayerItem>
        </Layer>
        {subtitles && (
          <div className="right-30 fixed bottom-8 left-1/2 z-20 -translate-x-[50%]">
            <Subtitle subtitles={subtitles} />
          </div>
        )}
      </div>
    </AbsoluteFill>
  );
};

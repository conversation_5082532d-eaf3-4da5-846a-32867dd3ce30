import { PlayerRef } from "@remotion/player";
import { MathJaxConfig } from "@repo/core/components/math-jax-config";
import { GuideProvider } from "@repo/core/guide/context/guide-context";
import {
  DrawElement,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import { FC, RefAttributes } from "react";
import { GuideCore } from "./guide-core";

interface GuideViewProps extends RefAttributes<PlayerRef> {
  data: GuideWidgetData;
  startFrame?: number;
  outFrame?: number;
  duration?: number;
  onDrawChange?: (paths: DrawElement[] | null) => void;
  eraserMode?: boolean;
  highlighter?: boolean;
  partIndex: number;
  totalPartsCount: number;
}

export const GuideView: FC<GuideViewProps> = ({
  data,
  startFrame = 0,
  outFrame = 0,
  duration,
  onDrawChange,
  eraserMode,
  highlighter,
  partIndex,
  totalPartsCount,
}) => {
  return (
    <MathJaxConfig>
      <GuideProvider
        data={data}
        index={partIndex}
        totalGuideCount={totalPartsCount}
      >
        <GuideCore
          startFrame={startFrame}
          outFrame={outFrame}
          duration={duration}
          onDrawChange={onDrawChange}
          eraserMode={eraserMode}
          highlighter={highlighter}
        />
      </GuideProvider>
    </MathJaxConfig>
  );
};

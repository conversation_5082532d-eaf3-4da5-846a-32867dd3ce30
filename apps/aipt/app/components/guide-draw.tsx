"use client";
import { GuideWidget, RawGuideSet } from "@/types/guide-widget";
import { FC } from "react";
import { GuideProvider } from "../context/guide-context";
import { GuideH3List } from "./draw-h3-list/h3-list";

interface GuideProps {
  guideSet: RawGuideSet;
  guideId: string;
  refreshSet?: () => void;
  children?: React.ReactNode;
  guideWidgetData?: GuideWidget;
  mutateGuideWidget: () => void;
}

export const GuideDraw: FC<GuideProps> = ({
  guideSet,
  // guideId,
  refreshSet,
  guideWidgetData,
  mutateGuideWidget,
}) => {
  if (!guideWidgetData) {
    return (
      <div className="flex h-full w-full items-center justify-center text-zinc-600">
        加载中...
      </div>
    );
  }

  return (
    <GuideProvider
      key={`${guideWidgetData.guideWidgetId}-${guideWidgetData.taskId}`}
      data={guideWidgetData}
      guideSet={guideSet}
      refresh={mutateGuideWidget}
      refreshSet={refreshSet}
    >
      <main className="min-w-[1150px] flex w-full flex-1 flex-col gap-4 p-2">
        <GuideH3List />
      </main>
    </GuideProvider>
  );
};

{"name": "aipt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3010", "build": "next build", "build:dev": "NODE_ENV=dev next build", "build:test": "NODE_ENV=test next build && node scripts/static-upload.js", "build:prod": "NODE_ENV=production next build && node scripts/static-upload.js", "start": "next start --port 3010", "lint": "next lint && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "build:local": "NODE_ENV=local next build", "deploy:dev": "NODE_ENV=local && next build && node scripts/static-upload.js && { pm2 del schroolroom-aipt || true; pm2 start -n schroolroom-aipt pnpm -- run start; }"}, "dependencies": {"@floating-ui/react": "^0.27.13", "@formkit/auto-animate": "^0.8.2", "@hookform/resolvers": "^5.0.1", "@preact-signals/safe-react": "^0.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/themes": "^3.2.1", "@remixicon/react": "^4.6.0", "@remotion/player": "catalog:remotion4", "@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@repo/core": "workspace:*", "@repo/lib": "workspace:*", "@repo/react-sketch-canvas": "workspace:*", "@repo/ui": "workspace:*", "@xyflow/react": "^12.5.5", "ahooks": "^3.8.4", "antd": "^5.25.3", "canvg": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "emittery": "^1.1.0", "json-diff-ts": "^4.2.3", "katex": "^0.16.21", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "micromark-extension-llm-math": "^3.1.0", "mitt": "^3.0.1", "next": "15.3.2", "react": "catalog:react19", "react-day-picker": "8.10.1", "react-dom": "catalog:react19", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-sketch-canvas": "^6.2.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-math": "^6.0.0", "remark-rehype": "^11.1.2", "remotion": "catalog:remotion4", "sonner": "^2.0.3", "swr": "^2.3.2", "tailwind-merge": "^3.0.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "ali-oss": "^6.22.0", "eslint-config-next": "15.3.0", "sass": "^1.89.2", "tailwindcss": "^4", "typescript": "^5", "unified": "^11.0.5"}}
import type { NextConfig } from "next";
import path from "path";

const assetPrefix =
  process.env.NODE_ENV === "production"
    ? "https://static.xiaoluxue.cn/aipt"
    : process.env.NODE_ENV === "test"
      ? "https://static.test.xiaoluxue.cn/aipt"
      : undefined;

console.log("assetPrefix ==>", assetPrefix, process.env.NODE_ENV);
const nextConfig: NextConfig = {
  // reactStrictMode: false,
  transpilePackages: [
    "@repo/ui",
    "@repo/lib",
    "@repo/core",
    "@repo/rough-notation",
  ],
  outputFileTracingRoot: path.resolve(__dirname, "../../"),
  distDir: "dist",
  output: "standalone",
  assetPrefix,
  /* config options here */
  experimental: {
    swcPlugins: [
      [
        "@preact-signals/safe-react/swc",
        {
          // you should use `auto` mode to track only components which uses `.value` access.
          // Can be useful to avoid tracking of server side components
          mode: "auto",
        } /* plugin options here */,
      ],
    ],
    turbo: {
      rules: {
        "*.svg": {
          loaders: [
            {
              loader: "@svgr/webpack",
              options: {
                icon: false,
              },
            },
          ],
          as: "*.js",
        },
      },
    },
  },

  // 配置重写规则
  rewrites: async () => {
    return [
      {
        source: "/api/:path*",
        destination: `http://lesson-kit-api.local.xiaoluxue.cn/api/:path*`,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.aliyuncs.com",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.cn",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.com",
        port: "",
        search: "",
      },
    ],
  },
  webpack: (config) => {
    config.resolve.alias["micromark-extension-math"] =
      "micromark-extension-llm-math";
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: false,
          },
        },
      ],
    });
    return config;
  },
  turbopack: {
    resolveAlias: {
      "micromark-extension-math": "micromark-extension-llm-math",
      "preact/signals-react": "@preact-signals/safe-react",
    },
  },
};

export default nextConfig;

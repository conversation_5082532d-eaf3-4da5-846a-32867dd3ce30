import { AvatarDropdown, AvatarName, Footer } from '@/components';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import '@ant-design/v5-patch-for-react-19';
import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { App, message } from 'antd';
import defaultSettings from '../config/defaultSettings';
import { goLogin } from './pages/user/login';
import { errorConfig } from './requestErrorConfig';
import { getProvinceList, getUserInfo, logout } from './services/user';
import { parseQueryString, saveTokenFormUrl } from './utils/common';
import { validateFingerprint } from './utils/fingerprint';
// import { currentUser as queryCurrentUser } from './services/ant-design-pro/api';

// Stagewise 工具栏配置 - 支持 Roo Code
// import { initToolbar } from '@stagewise/toolbar';
import { RegionItem } from './services/partner-school/types';

const isDev = process.env.NODE_ENV === 'development';

// 初始化 Stagewise 工具栏（仅在开发环境）
// if (isDev) {
//   // 延迟初始化，确保 DOM 已加载
//   setTimeout(() => {
//     initToolbar({
//       plugins: [], // 可以添加自定义插件
//     });
//     console.log('🚀 Stagewise 已启动，支持 Roo Code AI 代理');
//   }, 1000);
// }
const loginPath = '/user/login';

const fetchUserInfo = async () => {
  const token = saveTokenFormUrl();
  if (!token) {
    return undefined;
  }
  const res = await getUserInfo();
  return res.data;
};

const fetchProvinceList = async () => {
  const res = await getProvinceList();
  if (res.code === 0) {
    return res.data;
  }
  return [] as RegionItem[];
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.LoginResultData['user'];
  allProvinces?: RegionItem[];
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.LoginResultData['user'] | undefined>;
  fetchProvinceList?: () => Promise<RegionItem[]>;
}> {
  // 如果不是登录页面，执行
  const { location } = history;

  const { fp } = parseQueryString(window.location.search);
  const { isValid } = await validateFingerprint(fp || '', { debug: true });

  if (!isValid) {
    message.error('当前设备与登录设备不一致，请重新登录！');
    logout();
    goLogin();
    return {
      fetchUserInfo,
      fetchProvinceList,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  if (
    !['/user/register', '/user/register-result', '/approval-detail'].includes(location.pathname)
  ) {
    const currentUser = await fetchUserInfo();
    const allProvinces = await fetchProvinceList();
    // console.log('allProvinces', allProvinces);
    return {
      fetchUserInfo,
      currentUser,
      fetchProvinceList,
      allProvinces,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }
  return {
    fetchUserInfo,
    fetchProvinceList,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    contentStyle: {
      padding: '20px 24px',
    },
    actionsRender: () => [],
    avatarProps: {
      // src: initialState?.currentUser?.avatar,
      // src: ,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    waterMarkProps: {
      // content: initialState?.currentUser?.name,
    },
    footerRender: () => <Footer />,
    onPageChange: async () => {
      const { location } = history;
      // 检查 URL 中是否包含 token 参数
      const { currentUser } = initialState || {};

      const menus = currentUser?.menus || [];

      const isNotLoginPath =
        !location.pathname.includes(loginPath) && !window.location.href.includes(loginPath);
      // console.log(`onPageChange `, {
      //   location,
      //   menus,
      //   initialState,
      //   pathname: location.pathname,
      //   href: window.location.href,
      //   loginPath,
      //   isNotLoginPath,
      //   // urlWithoutToken,
      // });
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && !localStorage.getItem('token') && isNotLoginPath) {
        return goLogin();
      } else if (isNotLoginPath) {
        return;
      }

      // 如果 menus 为空，重定向到 /中处理，防止出现403页面闪烁
      if (!menus?.length) {
        return history.push('/home-transfer');
      }
    },
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    links: [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <App className="admin-app-wrapper">
          {children}
          {isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </App>
      );
    },
    ...initialState?.settings,
  };
};

// 获取当前环境
const env = process.env;
const baseURL = env.NODE_ENV === 'development' ? '' : API_URL || '';
console.log('env', env, baseURL, REACT_APP_ENV);

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  baseURL: baseURL,
  ...errorConfig,
};

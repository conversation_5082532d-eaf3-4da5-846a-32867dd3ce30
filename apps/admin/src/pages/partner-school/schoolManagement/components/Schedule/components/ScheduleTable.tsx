import { DictTypeEnum } from '@/services/dict';
import { getWithQuery, post } from '@/services/fetcher';
import {
  PeriodInfo,
  Schedule,
  ScheduleData,
  ScheduleDayItem,
  TplInfo,
} from '@/services/schedule-management/type';
import { getDateByWeekday, isBeforeToday } from '@/utils/date';
import { DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Drawer,
  message as antdMessage,
  Modal,
  Radio,
  Select,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import {
  SchoolSubjectItem,
  SchoolSubjectTeacherData,
  SchoolSubjectTeacherItem,
} from '../../../types';
import { TeacherInfo, TeacherListParams } from '../../TeacherAccount/type';
import { WeekInfo } from './SemesterWeekSelector/useSemesterWeekSelector';
const { Text } = Typography;

interface ScheduleTableProps {
  weekInfo: WeekInfo;

  scheduleData: ScheduleData;
  isGradeTemplate: boolean;
  isEditMode?: boolean;
  schoolId?: number;
  gradeId?: number;
  classId?: number;
  onSave?: (modifiedSchedule: Record<string, ScheduleDayItem[]>) => void;
  onCancel?: () => void;
}

/**
 * 时间段名称映射
 */
const TIME_SPAN_MAPPING: Record<number, string> = {
  1: '上午',
  2: '下午',
  3: '晚上',
};

// 课程类型选项
const COURSE_TYPE_OPTIONS = [
  { label: '课程', value: 1 },
  { label: '学科自习', value: 2 },
  { label: '自由自习', value: 3 },
  // { label: '休息', value: 4 },
];

// 已选课节信息接口
interface SelectedClassPeriod {
  day: number;
  dayName: string;
  periodId: number;
  periodNo: number;
  startTime: string;
  endTime: string;
  // 保存原始单元格数据，方便对接后端
  cellData: {
    period: PeriodInfo;
    dayId: keyof Schedule;
  };
}

interface SetScheduleParams {
  class_id: number;
  week_start_date: string;
  week_end_date: string;
  schedule: Record<string, ScheduleDayItem[]>;
  tpl_info: TplInfo;
}
/**
 * 课程表组件
 *
 * @param scheduleData - 课程表数据
 * @param isGradeTemplate - 是否为年级模板视图
 * @param isEditMode - 是否为编辑模式
 * @param schoolId - 学校ID
 * @param gradeId - 年级ID
 * @param classId - 班级ID
 */
const ScheduleTable: React.FC<ScheduleTableProps> = ({
  weekInfo,
  onSave,
  onCancel,
  scheduleData,
  isGradeTemplate,
  isEditMode = false,
  schoolId,
  gradeId,
  classId,
}) => {
  const [message, messageContextHolder] = antdMessage.useMessage();
  const { dictCache } = useModel('dictModels');
  console.log('字典: ', dictCache);

  // 当前加载的课表数据ID引用，用于比较scheduleData是否发生变化
  const scheduleDataRef = useRef<string>('');

  const { trigger: mutateSetSchedule } = useSWRMutation(
    '/api/v1/schedule/set',
    post<any, SetScheduleParams>,
  );

  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);

  // 判断单元格是否应该被禁用
  const isCellDisabled = (dayIndex: number) => {
    if (!weekInfo?.startDate) return false;

    // 根据周几获取日期
    const cellDate = getDateByWeekday(weekInfo.startDate, dayIndex);
    return isBeforeToday(cellDate, true);
  };

  // 获取单元格日期
  const getCellDate = (dayIndex: number): string => {
    if (!weekInfo?.startDate) return '';
    return getDateByWeekday(weekInfo.startDate, dayIndex);
  };

  // 生成暂存数据的唯一key
  const localStorageKey = `schedule_temp_${schoolId}_${gradeId}_${classId}`;
  console.log('localStorageKey', localStorageKey);

  // 本地修改后的课程表数据
  const [modifiedSchedule, setModifiedSchedule] = useState<Record<string, ScheduleDayItem[]>>({});

  // 选中的单元格状态
  const [selectedCells, setSelectedCells] = useState<SelectedClassPeriod[]>([]);

  // 抽屉可见状态
  const [drawerVisible, setDrawerVisible] = useState(false);

  // 课程设置表单状态
  const [courseType, setCourseType] = useState<number>(1);
  const [subject, setSubject] = useState<number>(0);
  const [selectedTeacher, setSelectedTeacher] = useState<SchoolSubjectTeacherItem | undefined>(
    undefined,
  );
  const [temporaryChange, setTemporaryChange] = useState<number | undefined>(0);

  // 是否有未保存的修改
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 教师列表状态
  const [classTeacherData, setClassTeacherData] = useState<SchoolSubjectTeacherData>({});
  const [teacherList, setTeacherList] = useState<TeacherInfo[]>([]);
  const [teacherSearchLoading, setTeacherSearchLoading] = useState(false);
  const [classSubjectList, setClassSubjectList] = useState<{ label: string; value: number }[]>([]);

  const { trigger: mutateTeacherList } = useSWRMutation(
    '/api/v1/userSchool/listTeachers',
    post<TeacherInfo[], TeacherListParams>,
  );
  const { trigger: mutateSubjectList } = useSWRMutation(
    '/api/v1/class/subject',
    getWithQuery<SchoolSubjectItem[]>,
  );
  const { trigger: mutateSubjectTeacherData } = useSWRMutation(
    '/api/v1/class/subjectTeacher',
    getWithQuery<SchoolSubjectTeacherData>,
  );
  const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const subjectTeacherList = useMemo<SchoolSubjectTeacherItem[]>(() => {
    return classTeacherData[subject] || [];
  }, [classTeacherData, subject]);

  useEffect(() => {
    console.log('=======>', subjectTeacherList, subject);
  }, [subjectTeacherList, subject]);

  // 生成当前scheduleData的唯一标识
  const getCurrentScheduleDataId = () => {
    return `${scheduleData?.tpl_info?.template_id || ''}_${scheduleData?.tpl_info?.semester_id || ''}_${new Date(scheduleData?.tpl_info?.start_date || '').getTime()}`;
  };

  // 重置所有状态
  const resetAllStates = () => {
    // 重置修改后的课表数据
    setModifiedSchedule(JSON.parse(JSON.stringify(scheduleData.schedule || {})));

    // 清除选中状态
    setSelectedCells([]);

    // 重置未保存状态
    setHasUnsavedChanges(false);

    // 重置表单状态
    setCourseType(1);
    setSubject(0);
    setSelectedTeacher(undefined);
    setTemporaryChange(0);

    // 关闭抽屉
    setDrawerVisible(false);

    // 清除暂存数据
    localStorage.removeItem(localStorageKey);
  };

  // 当scheduleData变化时重置状态
  useEffect(() => {
    const currentId = getCurrentScheduleDataId();

    // 如果scheduleData变化，重置状态
    if (scheduleDataRef.current !== currentId) {
      setLoading(true);

      // 更新引用ID
      scheduleDataRef.current = currentId;

      // 初始化修改后的课表数据为scheduleData中的数据
      setModifiedSchedule(JSON.parse(JSON.stringify(scheduleData.schedule || {})));

      // 延迟关闭loading，给计算留出时间
      setTimeout(() => {
        setLoading(false);
      }, 300);
    }
  }, [scheduleData]);

  // 检查是否有暂存数据并提示用户
  useEffect(() => {
    if (isEditMode) {
      const tempData = localStorage.getItem(localStorageKey);
      if (tempData) {
        Modal.confirm({
          title: '发现暂存数据',
          icon: <ExclamationCircleOutlined />,
          content: '检测到您上次有未保存的修改，是否加载暂存数据？',
          okText: '加载',
          cancelText: '不加载',
          onOk() {
            try {
              const parsedData = JSON.parse(tempData);
              setModifiedSchedule(parsedData.modifiedSchedule || {});
              message.success('已加载暂存数据');
              setHasUnsavedChanges(true);
            } catch (e) {
              message.error('暂存数据加载失败');
              localStorage.removeItem(localStorageKey);
            }
          },
          onCancel() {
            localStorage.removeItem(localStorageKey);
          },
        });
      }
      if (scheduleData.schedule) {
        setModifiedSchedule(JSON.parse(JSON.stringify(scheduleData.schedule || {})));
      }
    }
  }, [isEditMode, localStorageKey]);

  const preloadSubjectAndTeacherList = async () => {
    setTeacherSearchLoading(true);
    try {
      const res = await Promise.allSettled([
        mutateSubjectList({ classId }),
        mutateSubjectTeacherData({ classId }),
      ]);
      const [subjectList, teacherList] = res;
      if (subjectList.status === 'fulfilled') {
        const subjectData = subjectList.value ?? [];
        if (subjectData) {
          const subjectList = subjectData.map((item) => ({
            value: item.subjectId,
            label: item.subjectName,
          }));
          const first = subjectList[0]?.value ?? 0;
          setClassSubjectList(subjectList);
          setSubject(first);
        } else {
          setClassSubjectList([]);
          setSubject(0);
        }
        if (teacherList.status === 'fulfilled') {
          const teacherData = teacherList.value;
          if (teacherData) {
            setClassTeacherData(teacherData);
          }
        } else {
          setClassTeacherData({});
        }
      }
      console.log('预加载班级的学科和班级的教师列表', res);
    } catch (error) {
      console.error('预加载班级的学科和班级的教师列表失败:', error);
    } finally {
      setTeacherSearchLoading(false);
    }
  };
  // 预加载班级的学科和班级的教师列表
  useEffect(() => {
    if (schoolId && isEditMode) {
      preloadSubjectAndTeacherList();
    }
  }, [schoolId, isEditMode]);

  // 从 scheduleData 中提取学期和日期范围信息
  const semesterInfo = scheduleData.tpl_info.semester_name;
  const dateRange = `${scheduleData.tpl_info.start_date} 起 ${scheduleData.tpl_info.week_cycle}周循环`;

  // 按时间段分组处理课时信息
  const groupedPeriods = useMemo(() => {
    console.log('groupedPeriods', scheduleData);

    // 设置加载状态
    setLoading(true);

    const periods = scheduleData.tpl_info.period_info;
    const grouped: Record<number, PeriodInfo[]> = {};

    // 根据时间段进行分组
    periods.forEach((period) => {
      // 获取时间段类型
      const spanType =
        period.schedule_tpl_period_time_span === '上午'
          ? 1
          : period.schedule_tpl_period_time_span === '下午'
            ? 2
            : 3;

      if (!grouped[spanType]) {
        grouped[spanType] = [];
      }

      grouped[spanType].push(period);
    });

    // 转换为数组格式，按时间段排序
    const result = Object.keys(grouped)
      .map(Number)
      .sort((a, b) => a - b)
      .map((spanType) => ({
        spanType,
        title: TIME_SPAN_MAPPING[spanType] || `时间段${spanType}`,
        periods: grouped[spanType].sort(
          (a, b) => a.schedule_tpl_period_period_no - b.schedule_tpl_period_period_no,
        ),
      }));

    // 使用setTimeout确保状态更新在下一个事件循环中
    setTimeout(() => {
      setLoading(false);
    }, 100);

    return result;
  }, [scheduleData.tpl_info.period_info]);

  // 构建查找期号对应课程的辅助函数
  const getCourseByPeriodAndDay = (periodId: number, dayId: keyof Schedule) => {
    if (isGradeTemplate) return null;

    const daySchedule = isEditMode ? modifiedSchedule[dayId] : scheduleData.schedule?.[dayId];
    if (!daySchedule) return null;

    // 查找匹配的课程
    return daySchedule.find((item) => item.schedule_tpl_period_id === periodId);
  };

  // 检查单元格是否被选中
  const isCellSelected = (periodId: number, dayId: number) => {
    return selectedCells.some((cell) => cell.periodId === periodId && cell.day === dayId);
  };

  // 处理单元格点击事件
  const handleCellClick = (period: PeriodInfo, dayId: keyof Schedule, dayIndex: number) => {
    if (!isEditMode) return;

    // 获取单元格日期和详细信息
    const cellDate = getCellDate(dayIndex);
    const cellInfo = {
      periodNo: period.schedule_tpl_period_period_no,
      weekday: weekdays[dayIndex],
      date: cellDate,
      timeSpan:
        TIME_SPAN_MAPPING[
          period.schedule_tpl_period_time_span === '上午'
            ? 1
            : period.schedule_tpl_period_time_span === '下午'
              ? 2
              : 3
        ] || period.schedule_tpl_period_time_span,
      startTime: period.schedule_tpl_period_start_time,
      endTime: period.schedule_tpl_period_end_time,
      isDisabled: isBeforeToday(cellDate, true),
    };

    // 打印单元格信息
    console.log('单元格详细信息:', cellInfo);

    // 检查单元格是否被禁用
    if (cellInfo.isDisabled) {
      console.log('单元格已禁用，禁止点击', cellInfo);
      return;
    }

    const day = Number(dayId);
    const isSelected = isCellSelected(period.schedule_tpl_period_id, day);

    if (isSelected) {
      // 如果已选中，则取消选中
      setSelectedCells(
        selectedCells.filter(
          (cell) => !(cell.periodId === period.schedule_tpl_period_id && cell.day === day),
        ),
      );
    } else {
      // 如果未选中，则添加到选中列表
      const newSelectedPeriod: SelectedClassPeriod = {
        day: day,
        dayName: weekdays[dayIndex],
        periodId: period.schedule_tpl_period_id,
        periodNo: period.schedule_tpl_period_period_no,
        startTime: period.schedule_tpl_period_start_time,
        endTime: period.schedule_tpl_period_end_time,
        cellData: {
          period,
          dayId,
        },
      };
      setSelectedCells([...selectedCells, newSelectedPeriod]);
    }
  };

  // 删除已选课节
  const handleDeleteSelectedPeriod = (index: number) => {
    const newSelected = [...selectedCells];
    newSelected.splice(index, 1);
    setSelectedCells(newSelected);
  };

  // 打开课程设置抽屉
  const openCourseDrawer = async () => {
    setDrawerVisible(true);

    try {
      const response = await mutateSubjectList({
        classId,
      });
      console.log('班级科目列表', response);
      if (response) {
        const subjectList = response.map((item) => ({
          value: item.subjectId,
          label: item.subjectName,
        }));
        const first = subjectList[0]?.value ?? 0;
        setClassSubjectList(subjectList);
        setSubject(first);
      }
    } catch (error) {
      setClassSubjectList([]);
      setSubject(0);
      console.error('加载班级科目列表失败:', error);
    }
  };

  // 关闭课程设置抽屉
  const handleCloseDrawer = () => {
    setDrawerVisible(false);
  };

  // 应用课程设置到本地课表
  const applyCourseSettings = () => {
    console.log('applyCourseSettings', {
      courseType,
      subject,
      selectedTeacher,
      temporaryChange,
      selectedCells,
    });

    if (!selectedTeacher?.teacherId) {
      message.error('请选择教师');
      return;
    }

    // 深拷贝当前修改后的课表
    const newModifiedSchedule = JSON.parse(JSON.stringify(modifiedSchedule));

    // 这里的学科名是从字典中获取的，如果从当前班的学科中获取会不会影响历史数据？？
    const subjectName =
      dictCache[DictTypeEnum.SUBJECT]?.find((item) => item.value === subject)?.label || '';

    // 将课程数据应用到所有选中的单元格
    selectedCells.forEach((cell) => {
      const { dayId } = cell.cellData;
      let daySchedule = newModifiedSchedule[dayId];

      if (!daySchedule) {
        // 如果该天没有课程数据，创建一个空数组
        daySchedule = newModifiedSchedule[dayId] = [];
      }

      // 查找要更新的课程
      const courseIndex = daySchedule.findIndex(
        (item: ScheduleDayItem) => item.schedule_tpl_period_id === cell.periodId,
      );

      if (courseIndex >= 0) {
        // 更新现有课程
        daySchedule[courseIndex] = {
          ...daySchedule[courseIndex],
          class_schedule_study_type: courseType,
          class_schedule_course: subjectName,
          class_schedule_course_id: subject,
          teacher_name: selectedTeacher?.teacherName,
          class_schedule_teacher_id: selectedTeacher?.teacherId,
          is_modify: 1,
          is_delete: 0,
          is_tmp: temporaryChange,
          // 可以根据需要添加其他字段
        };
      } else {
        // 添加新课程
        daySchedule.push({
          schedule_tpl_period_id: cell.periodId,
          class_schedule_study_type: courseType,
          class_schedule_course: subjectName,
          class_schedule_course_id: subject,
          teacher_name: selectedTeacher?.teacherName,
          class_schedule_teacher_id: selectedTeacher?.teacherId,
          is_modify: 1,
          is_tmp: temporaryChange,
          // 其他必要字段
        });
      }
    });

    // 更新本地修改后的课表数据
    setModifiedSchedule(newModifiedSchedule);

    // 标记有未保存的修改
    setHasUnsavedChanges(true);

    // 清除选中的单元格
    setSelectedCells([]);

    // 关闭抽屉
    setDrawerVisible(false);

    console.log('modifiedSchedule', { modifiedSchedule, selectedCells });

    // 提示用户
    message.success('课程设置已应用，请记得保存');
  };

  // 暂存当前修改
  const saveTempData = () => {
    const tempData = {
      modifiedSchedule,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem(localStorageKey, JSON.stringify(tempData));
    message.success('修改已暂存');
  };

  // 获取学科名称
  const getSubjectName = (course: ScheduleDayItem) => {
    console.log('course', course);
    const subjectName = dictCache[DictTypeEnum.SUBJECT]?.find(
      (item) => item.value === course.class_schedule_course_id,
    )?.label;
    switch (course.class_schedule_study_type) {
      case 1:
        return subjectName;
      case 2:
        return subjectName ? subjectName + '自习' : '学科自习';
      case 3:
        return '自习';
      // case 4:
      //   return '休息'
      default:
        return subjectName;
    }
  };

  // 清除单元格内容
  const handleClearCell = (period: PeriodInfo, dayId: keyof Schedule, dayIndex: number) => {
    console.log('单元格删除： ', period, dayId, dayIndex);
    if (!isEditMode) return;

    // 检查单元格是否被禁用
    const cellDate = getCellDate(dayIndex);
    if (isBeforeToday(cellDate)) {
      message.warning('无法清除过期日期的课程安排');
      return;
    }

    // 打印单元格清除信息
    console.log('清除单元格:', {
      periodNo: period.schedule_tpl_period_period_no,
      weekday: weekdays[dayIndex],
      date: cellDate,
      dayId,
    });

    // 深拷贝当前修改后的课表
    const newModifiedSchedule = JSON.parse(JSON.stringify(modifiedSchedule));

    // 获取当天的课表数据
    const daySchedule = newModifiedSchedule[dayId];
    if (!daySchedule) return;

    // 查找要清除的课程在数组中的索引
    const courseIndex = daySchedule.findIndex(
      (item: ScheduleDayItem) => item.schedule_tpl_period_id === period.schedule_tpl_period_id,
    );

    if (courseIndex >= 0) {
      // 清除字段class_schedule_teacher_id、teacher_name、is_modify、is_tmp、class_schedule_course_id、class_schedule_course、class_schedule_study_type
      daySchedule[courseIndex] = {
        ...daySchedule[courseIndex],
        is_delete: 1,
        class_schedule_teacher_id: undefined,
        teacher_name: undefined,
        is_modify: 1,
        is_tmp: 0,
        class_schedule_course_id: undefined,
        class_schedule_course: undefined,
        class_schedule_study_type: undefined,
      };

      // 更新本地修改后的课表数据
      setModifiedSchedule(newModifiedSchedule);

      // 标记有未保存的修改
      setHasUnsavedChanges(true);

      // 提示用户
      message.success('课程已清除，请记得保存');
    }
  };

  // 处理取消编辑
  const handleCancel = () => {
    if (hasUnsavedChanges) {
      Modal.confirm({
        title: '未保存的修改',
        icon: <ExclamationCircleOutlined />,
        content: '您有未保存的修改，确定要取消吗？',
        okText: '确定',
        cancelText: '返回编辑',
        okButtonProps: { danger: true },
        onOk() {
          console.log('取消编辑');
          setModifiedSchedule(JSON.parse(JSON.stringify(scheduleData.schedule || {})));
          setSelectedCells([]);
          onCancel?.();
        },
        cancelButtonProps: { type: 'primary' },
        footer: (_, { OkBtn, CancelBtn }) => (
          <>
            <Button onClick={saveTempData}>暂存</Button>
            <CancelBtn />
            <OkBtn />
          </>
        ),
      });
    } else {
      // 如果没有未保存的修改，直接取消
      onCancel?.();
    }
  };

  // 保存课程表修改
  const handleSave = async () => {
    // 这里调用保存接口
    const params: SetScheduleParams = {
      class_id: classId!,
      week_start_date: weekInfo.startDate,
      week_end_date: weekInfo.endDate,
      schedule: {},
      tpl_info: scheduleData.tpl_info,
    };
    console.log('保存课程表修改', {
      params,
      modifiedSchedule,
      groupedPeriods,
      weekInfo,
      scheduleData,
    });
    const newModifiedSchedule = JSON.parse(JSON.stringify(modifiedSchedule));
    // 筛选出is_modify为1的课程
    Object.values(newModifiedSchedule as Record<string, ScheduleDayItem[]>).forEach(
      (items: ScheduleDayItem[], index: number) => {
        console.log('items', { items, newModifiedSchedule, index });

        newModifiedSchedule[index + 1] = items.filter(
          (item: ScheduleDayItem) => item.is_modify === 1 || item.is_delete === 1,
        );
      },
    );
    params.schedule = newModifiedSchedule;
    console.log('保存课程表修改 newModifiedSchedule', newModifiedSchedule);
    try {
      const response = await mutateSetSchedule(params);
      console.log('response', response);
      // 保存成功后
      message.success('保存成功');
    } catch (e) {
      console.error('保存失败');
    } finally {
      // 重置所有状态
      resetAllStates();

      // 调用父组件的保存回调
      onSave?.(modifiedSchedule);
    }

    // // 重置所有状态
    // resetAllStates();

    // // 调用父组件的保存回调
    // onSave?.(modifiedSchedule);
  };

  // 渲染课表内容
  const renderScheduleTable = useMemo(() => {
    return (
      <table className="w-full border-separate border-spacing-0">
        <thead>
          <tr>
            <th className="border-t border-l border-gray-200 bg-gray-50 p-2"></th>
            <th className="border-t border-l border-gray-200 bg-gray-50 p-2 w-24">节次/时间</th>
            {weekdays.map((day, index) => {
              const isDisabled = isCellDisabled(index);
              const cellDate = getCellDate(index);
              return (
                <th
                  key={index}
                  className={`
                    border-t border-l ${index === weekdays.length - 1 ? 'border-r' : ''}
                    border-gray-200 bg-gray-50 p-2 min-w-[120px]
                    ${isDisabled ? 'text-gray-400' : ''}
                  `}
                >
                  <div>{day}</div>
                  <div className="text-xs">{cellDate.replace(/-/g, '.')}</div>
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody>
          {groupedPeriods.map((group, groupIndex) => (
            <React.Fragment key={group.spanType}>
              {group.periods.map((period, periodIndex) => (
                <tr key={period.schedule_tpl_period_id}>
                  {/* 时间段标题（只在每组第一行显示） */}
                  {periodIndex === 0 && (
                    <td
                      className={`border-t border-l border-gray-200 bg-gray-50 p-2 font-medium text-center whitespace-nowrap ${groupIndex === groupedPeriods.length - 1 ? 'border-b' : ''}`}
                      rowSpan={group.periods.length}
                    >
                      {group.title}
                    </td>
                  )}

                  {/* 课时信息 */}
                  <td
                    className={` border-l border-gray-200 bg-gray-50 p-2 ${groupIndex === groupedPeriods.length - 1 ? 'border-b' : 'border-t '} ${periodIndex === 0 && groupIndex === groupedPeriods.length - 1 ? 'border-t' : ''}`}
                  >
                    <div className="flex flex-col">
                      <div className="whitespace-nowrap">
                        第{period.schedule_tpl_period_period_no}节
                      </div>
                      <div className="text-xs text-gray-500 mt-1 whitespace-nowrap">
                        {period.schedule_tpl_period_start_time} 至{' '}
                        {period.schedule_tpl_period_end_time}
                      </div>
                    </div>
                  </td>

                  {/* 周一至周日的课程内容 */}
                  {weekdays.map((_, dayIndex) => {
                    const dayId = (dayIndex + 1).toString() as keyof Schedule;
                    const course = getCourseByPeriodAndDay(period.schedule_tpl_period_id, dayId);
                    const isLastColumn = dayIndex === weekdays.length - 1;
                    const isLastRow =
                      group.spanType === groupedPeriods[groupedPeriods.length - 1].spanType &&
                      periodIndex === group.periods.length - 1;

                    const isSelected = isCellSelected(period.schedule_tpl_period_id, Number(dayId));
                    const isModify = isSelected ? 0 : course?.is_modify;
                    const isDisabled = isCellDisabled(dayIndex);
                    const cellDate = getCellDate(dayIndex);
                    const hasCourse = course && course.class_schedule_study_type;

                    return (
                      <td
                        key={dayId}
                        className={`relative
                          border-t border-l ${isLastColumn ? 'border-r' : ''}
                          ${isLastRow ? 'border-b' : ''}
                          border-gray-200 p-2 h-[60px] align-middle
                          ${isEditMode && !isDisabled ? 'cursor-pointer ' : ''}
                          ${!isSelected && !isModify && isEditMode ? 'hover:bg-gray-50' : ''}
                          ${isDisabled ? 'bg-gray-50 opacity-50' : ''}
                          ${isSelected ? 'bg-blue-50 ' : ''}
                          ${isModify ? 'bg-red-50 ' : ''}
                        `}
                        onClick={() => handleCellClick(period, dayId, dayIndex)}
                        data-date={cellDate}
                        data-period={period.schedule_tpl_period_period_no}
                        data-weekday={weekdays[dayIndex]}
                      >
                        {hasCourse ? (
                          <div className={` p-1 rounded ${isDisabled ? 'text-gray-400' : ''}`}>
                            <div className="font-medium">{getSubjectName(course)}</div>
                            {course.teacher_name && (
                              <div className="text-xs text-gray-600">{course.teacher_name}</div>
                            )}
                            <div className="text-xs text-gray-500 mt-1 hidden">
                              {weekdays[dayIndex]} {cellDate.replace(/-/g, '.')}
                            </div>
                            {isEditMode && !isDisabled && (
                              <div
                                className="absolute "
                                style={{
                                  top: '2px',
                                  right: '2px',
                                }}
                              >
                                <Button
                                  type="text"
                                  size="small"
                                  icon={<DeleteOutlined />}
                                  className="border-none group-hover:opacity-100 hover:opacity-100 text-red-500 
                                          rounded-full flex items-center justify-center shadow-sm"
                                  onClick={(e) => {
                                    e.stopPropagation(); // 阻止冒泡，防止触发单元格点击事件
                                    handleClearCell(period, dayId, dayIndex);
                                  }}
                                ></Button>
                              </div>
                            )}
                          </div>
                        ) : null}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    );
  }, [groupedPeriods, weekdays, isEditMode, selectedCells]);

  return (
    <>
      <div className="flex flex-col h-full">
        <div className="">
          <div className="flex justify-between items-center pb-4">
            <div className="font-medium align-middle">
              {semesterInfo} ({dateRange})
            </div>

            {isEditMode && (
              <>
                <div className="text-xs text-gray-500">
                  建议：点击表格，按学科设置课程，当前已选 {selectedCells.length} 项
                </div>
                <div className="space-x-2 flex justify-center items-center">
                  <Button
                    type="primary"
                    onClick={openCourseDrawer}
                    disabled={selectedCells.length === 0}
                  >
                    填写课程内容
                  </Button>

                  <Button type="primary" onClick={handleSave} disabled={!hasUnsavedChanges}>
                    保存
                  </Button>
                  <Button onClick={handleCancel}>取消</Button>
                </div>
              </>
            )}
          </div>
          <div className="overflow-auto h-[calc(100vh-308px-100px-40px)]">
            <Spin spinning={loading} tip="正在加载课表...">
              {renderScheduleTable}
            </Spin>
          </div>
        </div>
      </div>

      {/* 课程设置抽屉 */}
      <Drawer
        title="课程设置"
        open={drawerVisible}
        onClose={handleCloseDrawer}
        width={400}
        footer={
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button onClick={handleCloseDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" onClick={applyCourseSettings}>
              确定
            </Button>
          </div>
        }
      >
        <div className="mb-6">
          <h3 className="text-base font-medium mb-3">已选课节</h3>
          <div className="  ">
            {selectedCells.map((cell, index) => (
              <div
                key={index}
                className="p-2 py-1 rounded bg-gray-50 flex justify-between items-center mb-2"
              >
                <div>
                  {cell.dayName}第{cell.periodNo}节课（{cell.startTime}～{cell.endTime}）
                </div>
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteSelectedPeriod(index)}
                />
              </div>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-base font-medium mb-3">修改内容</h3>

          <div className="mb-4">
            <h4 className="mb-2">类型</h4>
            <Radio.Group
              options={COURSE_TYPE_OPTIONS}
              value={courseType}
              onChange={(e) => {
                setCourseType(e.target.value);
                setSubject(0);
              }}
            />
          </div>

          {[1, 2].includes(Number(courseType)) && (
            <div className="mb-4">
              <h4 className="mb-2">学科</h4>
              <Radio.Group
                options={classSubjectList}
                value={subject}
                onChange={(e) => {
                  setSubject(e.target.value);
                  setSelectedTeacher(undefined);
                }}
              />
            </div>
          )}

          <div className="mb-4">
            <h4 className="mb-2 ">
              <span>教师</span> <span className="text-red-500 align-middle">*</span>
            </h4>
            <Select
              allowClear
              placeholder="请选择教师"
              value={selectedTeacher?.teacherId}
              onChange={(value, option) => {
                const opt = Array.isArray(option) ? option[0] : option;
                setSelectedTeacher(opt);
              }}
              style={{ width: '100%' }}
              options={subjectTeacherList}
              fieldNames={{
                label: 'teacherName',
                value: 'teacherId',
              }}
            />
          </div>

          <div className="mb-4">
            <h4 className="mb-2">是否临时调课</h4>
            <Select
              placeholder="请选择是否临时调课"
              value={temporaryChange}
              onChange={(value) => setTemporaryChange(value)}
              style={{ width: '100%' }}
            >
              <Select.Option value={1}>仅本周修改</Select.Option>
              <Select.Option value={0}>所有周都修改</Select.Option>
            </Select>
          </div>
        </div>
      </Drawer>
      {messageContextHolder}
    </>
  );
};

export default ScheduleTable;

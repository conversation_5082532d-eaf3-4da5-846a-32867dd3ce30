import { useSignal } from "@preact-signals/safe-react";
import { Player } from "@remotion/player";

import { toast } from "@repo/core/components/stu-toast";
import { Guide } from "@repo/core/guide/guide";
import { Reference } from "@repo/core/types/data/comment";
import { GuideTheme } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback, useEffect } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { useGuideViewContext } from "./guide-view-context";

interface GuidePlayerViewProps {
  className?: string;
  theme?: GuideTheme;
}

export const GuidePlayerView: FC<GuidePlayerViewProps> = ({
  className,
  theme,
}) => {
  const {
    showSubtitle,
    playRate,
    refPlayer,
    title,
    totalGuideCount,
    index,
    data,
    guideMode,
    setFollowMode,
    seekTo,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    screen,
    togglePlay,
    trackEventWithLessonId,
    refContainer,
    commentRef,
    ranges,
    referenceList,
    onClickReference,
    goto,
    active,
  } = useGuideViewContext();

  const showLastProgressToast = useSignal(false);
  const isComment = useSignal(false);

  const longPressHandlers = useLongPress(
    (e) => {
      // todo)): 这里传的太麻烦，我先用dom直接弄了
      const [...doms] = document.querySelectorAll("[data-name=line-container]");
      if (doms.some((dom) => dom.contains(e.target as HTMLElement))) {
        isComment.value = true;
        return;
      }
      set3XPlayRate();
    },
    {
      onFinish: () => {
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        resetPlayRate();
        trackEventWithLessonId("doc_fast_forward_longpress");
      },
      detect: LongPressEventType.Touch,
    }
  );

  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay, trackEventWithLessonId]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  const handleLineClick = (frame: number) => {
    seekTo(frame);
    setFollowMode();
    trackEventWithLessonId("doc_learn_from_here_click");
  };

  const handleScrollNext = (index: number) => {
    console.log("handleScrollNext", index);
    goto(index);
  };

  useEffect(() => {
    if (showLastProgressToast.value || !active) return;
    if (progress.frame > 0) {
      showLastProgressToast.value = true;
      toast.show("已从上次进度开始学习");
    }
    return () => {
      showLastProgressToast.value = false;
    };
  }, [progress, showLastProgressToast, active]);

  if (!data) {
    return <div>无数据</div>;
  }
  const { avatar } = data;
  const lineIdInRange = ranges[0]?.lineId;

  return (
    <div
      {...doubleTapHandlers}
      data-name="guide-player"
      className="h-full w-full"
      {...longPressHandlers()}
    >
      <Player
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={{ height: screen.height }}
        component={Guide}
        inputProps={{
          client: "stu",
          guideMode,
          title,
          index,
          totalGuideCount,
          data,
          theme,
          showSubtitle,
          onLineClick: handleLineClick,
          refContainer,
          commentRef,
          lineIdInRange,
          referenceList: referenceList as Reference[],
          onClickReference,
          onScrollFlip: handleScrollNext,
        }}
        initialFrame={progress.frame}
        durationInFrames={avatar.durationInFrames + avatar.fps} //增加1秒, bugfix: 最后一点语音未播完就结束
        fps={avatar.fps}
        playbackRate={playRate.value}
        // autoPlay
        // controls
        // alwaysShowControls
        allowFullscreen={false}
        compositionWidth={screen.width}
        compositionHeight={screen.height}
        // compositionWidth={1000}
        // compositionHeight={600}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
    </div>
  );
};

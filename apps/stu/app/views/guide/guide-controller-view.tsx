import { toast } from "@/app/components/common/toast";
import {
  FloatTextButton,
  TranslucentGlassButton,
} from "@/app/components/guide/guide-buttons";
import IconFastBackward from "@/public/icons/fast-backward.svg";
import IconFastForward from "@/public/icons/fast-forward.svg";
import IconPause from "@/public/icons/pause.svg";
import IconPlane from "@/public/icons/plane.svg";
import IconPlay from "@/public/icons/play.svg";
import IconClose from "@/public/icons/x.svg";
import { cn } from "@repo/ui/lib/utils";
import { FC, useEffect, useState } from "react";
import { useDebouncedCallback, useThrottledCallback } from "use-debounce";
import { GuideMode } from "../course/course-view-context";
import { useGuideViewContext } from "./guide-view-context";

const RateSelector: FC<{ onClose?: () => void }> = ({ onClose }) => {
  const rates = [0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  const { playRate, trackEventWithLessonId } = useGuideViewContext();

  const handleRateChange = (rate: number) => {
    playRate.value = rate;
    onClose?.();
    trackEventWithLessonId("doc_speed_change");
  };

  return (
    <div className="absolute bottom-0 right-0 w-max">
      <ul className="flex h-8 flex-row gap-2 rounded-bl-3xl rounded-tl-3xl bg-white from-white to-[#FEFDFA] px-4 py-1.5 shadow-[-4px_0px_8px_0px_rgba(64,43,26,0.05)] outline-1 outline-offset-[-1px] outline-white backdrop-blur-[5px]">
        {rates.map((rate, index) => (
          <ol
            key={index}
            onClick={() => handleRateChange(rate)}
            className={cn(
              "flex select-none flex-col items-center justify-center px-3 py-1 text-base font-medium leading-none transition-colors",
              rate === playRate.value
                ? "rounded-2xl bg-[#FF7B59] text-white"
                : "text-[#ADABA9]"
            )}
          >
            {rate}x
          </ol>
        ))}
        <ol
          onClick={onClose}
          className={`flex h-6 w-12 select-none items-center justify-center px-3 py-1.5 text-xs font-medium leading-3 text-[#ADABA9] transition-colors`}
        >
          <IconClose className="size-4" />
        </ol>
      </ul>
    </div>
  );
};

const FollowModeController: FC = () => {
  const {
    showSubtitle,
    setShowSubtitle,
    playRate,
    isPlaying,
    forwardTo,
    togglePlay,
    showPlayerControls,
    togglePlayerControls,
    trackEventWithLessonId,
  } = useGuideViewContext();
  const [showSelector, setShowSelector] = useState(false);

  // 3s 后无操作隐藏播放器控制器
  const hidePlayerControls = useDebouncedCallback(() => {
    setShowSelector(false);
    togglePlayerControls();
  }, 3000);

  const handleForwardTo = useThrottledCallback((frame: number) => {
    forwardTo(frame);
    trackEventWithLessonId("doc_seek_10s_click");
  }, 1000);

  useEffect(() => {
    if (showPlayerControls) {
      hidePlayerControls();
    }
  }, [showPlayerControls, hidePlayerControls]);

  if (!showPlayerControls) return null;

  return (
    <div
      data-name="guide-controller::follow"
      className="w-50 absolute bottom-8 right-4 h-1/2"
    >
      <div
        className="flex h-full w-full flex-col items-end justify-between"
        onClick={() => {
          hidePlayerControls();
        }}
      >
        <div className="flex flex-col items-end gap-3">
          <FloatTextButton
            onClick={() => setShowSubtitle(!showSubtitle)}
            label="字幕"
            value={showSubtitle ? "开" : "关"}
          />
          <div className="relative">
            <FloatTextButton
              onClick={() => setShowSelector(true)}
              label="倍速"
              value={playRate.toString()}
            />

            {showSelector && (
              <RateSelector onClose={() => setShowSelector(false)} />
            )}
          </div>
        </div>
        <div className="flex w-full flex-row items-center justify-center gap-3">
          <TranslucentGlassButton
            onClick={() => handleForwardTo(-10)}
            icon={<IconFastBackward />}
          />
          <TranslucentGlassButton
            className="w-18 h-12"
            onClick={togglePlay}
            icon={isPlaying ? <IconPause /> : <IconPlay />}
          />
          <TranslucentGlassButton
            onClick={() => handleForwardTo(10)}
            icon={<IconFastForward />}
          />
        </div>
      </div>
    </div>
  );
};

const FreeModeController: FC = () => {
  const { setFollowMode } = useGuideViewContext();

  const handleClick = () => {
    setFollowMode();
    toast.show("已定位到老师位置", {
      delay: 600,
    });
  };

  return (
    <div
      data-name="guide-controller::free"
      className="w-50 absolute bottom-8 right-4"
    >
      <div className="flex h-full w-full flex-col items-center justify-end">
        <TranslucentGlassButton icon={<IconPlane />} onClick={handleClick}>
          <span className="font-bold">跟随老师</span>
        </TranslucentGlassButton>
      </div>
    </div>
  );
};

export const GuideControllerView: FC = () => {
  const { guideMode } = useGuideViewContext();

  return (
    <>
      {guideMode === GuideMode.follow ? (
        <FollowModeController />
      ) : (
        <FreeModeController />
      )}
    </>
  );
};

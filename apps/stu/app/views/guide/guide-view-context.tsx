import { toast } from "@/app/components/common/toast";
import { useSelection } from "@/app/hooks/use-selection";
import { useReferencesList } from "@/app/models/comments/comments-model";
import { ApiGetReferencesResponse } from "@/app/models/comments/schemas";
import { surveillanceReport } from "@/app/utils/device";
import { GuideProgress } from "@/types/app/course";
import { WidgetViewProps } from "@/types/app/ui";
import {
  batch,
  ReadonlySignal,
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { CallbackListener, PlayerRef } from "@remotion/player";
import { MergedReference } from "@repo/core/types/data/comment";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import {
  createContext,
  FC,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { KeyedMutator } from "swr";
import {
  CourseViewContextType,
  GuideMode,
  useCourseViewContext,
} from "../course/course-view-context";
import { useGuideGesture } from "./hooks/use-guide-gesture";

export type GuideViewContextType = CourseViewContextType & {
  title?: string;
  index?: number;
  totalGuideCount: number;
  data: GuideWidgetData;
  refPlayer: RefObject<PlayerRef | null>;
  isPlaying: boolean;
  forwardTo: (seconds: number) => void;
  seekTo: (frame: number) => void;
  togglePlay: () => void;
  setFollowMode: () => void;
  setFreeMode: () => void;
  showPlayerControls: boolean;
  togglePlayerControls: () => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  progress: GuideProgress;
  commentsBarVisible: ReadonlySignal<boolean>;
  commentInputVisible: Signal<boolean>;
  commentRef: Signal<HTMLDivElement | null>;
  ranges: ReturnType<typeof useSelection>["ranges"];
  contextMenuPos: ReturnType<typeof useSelection>["pos"];
  referenceList?: ApiGetReferencesResponse["list"];
  refreshReferences: KeyedMutator<ApiGetReferencesResponse>;
  markedRanges: Signal<ReturnType<typeof useSelection>["ranges"]>;
  markedReference: Signal<MergedReference[string][string][number] | null>;
  onClickReference: (
    reference: MergedReference[string][string][number]
  ) => void;
  refContainer: RefObject<HTMLDivElement | null>; //文稿容器, 用户处理
  active: boolean;
};

const GuideViewContext = createContext<GuideViewContextType>(
  {} as GuideViewContextType
);

interface GuideViewContextProviderProps extends WidgetViewProps<"guide"> {
  children: React.ReactNode;
}

export const useGuideViewContext = () => {
  return useContext(GuideViewContext);
};

export const GuideViewContextProvider: FC<GuideViewContextProviderProps> = ({
  totalGuideCount,
  content,
  active,
  children,
}) => {
  const courseContexts = useCourseViewContext();

  const {
    lessonId,
    guideMode,
    setGuideMode,
    isProgressBarOpen,
    playRate,
    next,
    localProgressRecorder,
    reportCostTime,
    isVersionChanged,
    knowledgeName,
  } = courseContexts;
  const { data, name: title, index } = content;
  const { data: referenceList, refresh: refreshReferences } = useReferencesList(
    {
      objId: lessonId,
      objType: 100,
    }
  );

  const progress = useMemo(() => {
    return localProgressRecorder.load<GuideProgress>(index, {
      frame: 0,
    });
  }, [index, localProgressRecorder]);

  const refPlayer = useRef<PlayerRef | null>(null);
  const refContainer = useRef<HTMLDivElement | null>(null);

  const isPlaying = useSignal(false);
  const isPlayingBeforeOpenProgressBar = useSignal(false);
  const isPlayingBeforeDocumentHidden = useSignal(false);
  const isPlayingBeforeCommentBarOpen = useSignal(false);
  const isPlayingBeforeCommentInputOpen = useSignal(false);
  const showPlayerControls = useSignal(false);

  const playRateBeforeLongPress = useSignal(playRate.value);

  const lastTime = useSignal(Date.now());
  const costTime = useSignal(0);
  const commentInputVisible = useSignal(false);
  const commentRef = useSignal<HTMLDivElement | null>(null);
  const { ranges, pos } = useSelection();
  const markedRanges = useSignal<typeof ranges>([]);
  const markedReference = useSignal<
    MergedReference[string][string][number] | null
  >(null);
  const commentsBarVisible = useComputed(() => Boolean(markedReference.value));

  const onClickReference = useCallback(
    (ref: MergedReference[string][string][number]) => {
      markedReference.value = ref;
    },
    [markedReference]
  );
  const durationInFrames = data.avatar.durationInFrames;
  const fps = data.avatar.fps || 1;
  const videoDuration = (durationInFrames + fps) / fps;

  const surveillanceReportHandler = useCallback(
    (currentFrame: number, videoAction: "seek" | "pause" | "play" | "stop") => {
      surveillanceReport({
        operationType: 6,
        videoAction,
        videoPosition: currentFrame / fps,
        videoDuration,
        currentStudyContent: knowledgeName,
      });
    },
    [fps, knowledgeName, videoDuration]
  );

  useEffect(() => {
    if (isProgressBarOpen) {
      batch(() => {
        isPlayingBeforeOpenProgressBar.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeOpenProgressBar.peek();
    }
  }, [isProgressBarOpen, isPlaying, isPlayingBeforeOpenProgressBar]);

  useSignalEffect(() => {
    if (commentsBarVisible.value) {
      batch(() => {
        isPlayingBeforeCommentBarOpen.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeCommentBarOpen.peek();
    }
  });

  useSignalEffect(() => {
    if (commentInputVisible.value) {
      batch(() => {
        isPlayingBeforeCommentInputOpen.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeCommentInputOpen.peek();
    }
  });

  const forwardTo = useCallback(
    (seconds: number) => {
      if (!active) return;
      if (!refPlayer.current) {
        return;
      }
      const player = refPlayer.current;
      const currentFrame = player.getCurrentFrame() + seconds * fps;
      surveillanceReportHandler(currentFrame, "seek");
      player.seekTo(currentFrame);
    },
    [fps, surveillanceReportHandler, active]
  );

  const seekTo = useCallback(
    (frame: number) => {
      if (!active) return;
      if (!refPlayer.current) {
        return;
      }
      if (frame >= durationInFrames || frame < 0) {
        return;
      }
      const player = refPlayer.current;
      surveillanceReportHandler(frame, "seek");
      player.seekTo(frame);
      isPlaying.value = true;
    },
    [surveillanceReportHandler, isPlaying, durationInFrames, active]
  );

  const togglePlay = useCallback(() => {
    if (guideMode === GuideMode.free) return;
    isPlaying.value = !isPlaying.peek();
    if (isPlaying.value === false) {
      surveillanceReportHandler(
        refPlayer.current?.getCurrentFrame() || 0,
        "pause"
      );
    }
  }, [guideMode, isPlaying, surveillanceReportHandler]);

  const setFollowMode = useCallback(() => {
    if (guideMode === GuideMode.follow) return;
    setGuideMode(GuideMode.follow);
    isPlaying.value = true;
  }, [guideMode, setGuideMode, isPlaying]);

  const setFreeMode = useCallback(() => {
    if (guideMode === GuideMode.free) return;
    setGuideMode(GuideMode.free);
    isPlaying.value = false;
    toast.show("自由浏览课件");
  }, [guideMode, setGuideMode, isPlaying]);

  const togglePlayerControls = useCallback(() => {
    showPlayerControls.value = !showPlayerControls.peek();
  }, [showPlayerControls]);

  const set3XPlayRate = useCallback(() => {
    if (guideMode === GuideMode.free) return;
    if (playRate.value === 3) return;

    playRateBeforeLongPress.value = playRate.value;
    playRate.value = 3;
  }, [guideMode, playRate, playRateBeforeLongPress]);

  const resetPlayRate = useCallback(() => {
    if (playRate.value === playRateBeforeLongPress.value) return;
    playRate.value = playRateBeforeLongPress.value;
  }, [playRate, playRateBeforeLongPress]);

  // refPlayer 事件
  useEffect(() => {
    if (!refPlayer.current) {
      return;
    }
    const player = refPlayer.current;

    const handleEnded = () => {
      // 清除进度
      surveillanceReportHandler(player.getCurrentFrame(), "stop");
      localProgressRecorder.clear(index);
      next();
    };
    //the event is throttled to only fire a few times a second at most every 250ms.
    const handleTimeUpdate: CallbackListener<"timeupdate"> = (e) => {
      localProgressRecorder.save(index, {
        frame: e.detail.frame,
      });
    };

    const handleVisibilitychange = () => {
      if (document.visibilityState === "hidden") {
        batch(() => {
          isPlayingBeforeDocumentHidden.value = isPlaying.peek();
          isPlaying.value = false;
        });
      } else {
        isPlaying.value = isPlayingBeforeDocumentHidden.peek();
      }
    };

    player.addEventListener("ended", handleEnded);
    player.addEventListener("timeupdate", handleTimeUpdate);
    document.addEventListener("visibilitychange", handleVisibilitychange);

    return () => {
      player.removeEventListener("ended", handleEnded);
      player.removeEventListener("timeupdate", handleTimeUpdate);
      document.removeEventListener("visibilitychange", handleVisibilitychange);
    };
  }, [
    isPlaying,
    playRateBeforeLongPress,
    next,
    localProgressRecorder,
    index,
    isPlayingBeforeDocumentHidden,
    data.avatar.fps,
    data.avatar.durationInFrames,
    playRate,
    surveillanceReportHandler,
  ]);

  useEffect(() => {
    if (active && guideMode === GuideMode.follow) {
      isPlaying.value = true;
    } else {
      isPlaying.value = false;
    }
  }, [active, guideMode, isPlaying]);

  useSignalEffect(() => {
    if (isVersionChanged.value) {
      isPlaying.value = false;
    }
  });

  useSignalEffect(() => {
    if (isPlaying.value) {
      refPlayer.current?.play();
      surveillanceReportHandler(
        refPlayer.current?.getCurrentFrame() || 0,
        "play"
      );
    } else {
      refPlayer.current?.pause();
    }
  });

  useEffect(() => {
    if (active) {
      lastTime.value = Date.now();
    }
  }, [active, lastTime]);

  useEffect(() => {
    if (!active) return;

    const updateTime = () => {
      const now = Date.now();
      costTime.value += now - lastTime.value;
      lastTime.value = now;
      // console.log({ duration: costTime.value });
      reportCostTime(costTime.value);
    };
    const timer = setInterval(updateTime, 250);
    return () => {
      clearInterval(timer);
      updateTime();
    };
  }, [active, reportCostTime, lastTime, costTime]);

  useGuideGesture(refContainer, setFreeMode);

  const value = {
    ...courseContexts,
    refPlayer,
    isPlaying: isPlaying.value,
    title,
    index,
    totalGuideCount,
    data,
    forwardTo,
    seekTo,
    togglePlay,
    setFollowMode,
    setFreeMode,
    showPlayerControls: showPlayerControls.value,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    refContainer,
    screen,
    commentsBarVisible,
    commentInputVisible,
    commentRef,
    ranges,
    contextMenuPos: pos,
    referenceList,
    refreshReferences,
    markedRanges,
    onClickReference,
    markedReference,
    active,
  };

  return <GuideViewContext value={value}>{children}</GuideViewContext>;
};

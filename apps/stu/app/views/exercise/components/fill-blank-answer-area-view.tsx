import { useQuestionContext } from "@/app/contexts/question-context";
import { FillBlankViewModel } from "@/app/viewmodels/exercise/fill-blank-question-viewmodel";
import { FormatMath } from "@repo/core/exercise/components";
import HalfRightIconNormalSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-normal.svg";
import HalfRightIconRedSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-red.svg";
import RightIconGreenSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-green.svg";
import RightIconNormalSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-normal.svg";
import WrongIconNormalSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-normal.svg";
import WrongIconRedSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-red.svg";
import { cn } from "@repo/ui/lib/utils";
import { Camera, Keyboard } from "lucide-react";
import React, { useEffect, useRef } from "react";
import CameraInput from "./camera-view";
import KeyboardInput from "./keyboard-input-view";

export type BlankAnswer = {
  id: number;
  value: string;
};
export type InputMode = "keyboard" | "camera";

export const AnswerAreaView: React.FC<{
  type: "fill-blank" | "solution" | "english-fill-blank";
  viewModel: FillBlankViewModel;
}> = ({ type, viewModel }) => {
  // 🎯 只需要从统一Context获取题目数据
  const {
    currentQuestion: question,
    updateUserAnswer,
    isSelfEvaluationSubmitted,
  } = useQuestionContext();

  // 使用填空题专用的 viewModel
  const fillBlankViewModel = viewModel;

  const {
    answers,
    activeBlankIndex,
    inputMode,
    imageFiles,
    handleModeChange,
    handleRemoveImage,
    handleRetryUpload,
    correctAnswers,
    selfEvaluation,
    isReview,
    triggerNativeImageUpload,
    setActiveBlankIndex,
    isEnglishFillBlank,
  } = fillBlankViewModel;
  const tabRefs = useRef<(HTMLLIElement | null)[]>([]);
  // 将答案同步到 QuestionContext 的 userAnswerData
  useEffect(() => {
    if (inputMode === "camera") {
      console.log("imageFiles", imageFiles);
      updateUserAnswer({
        inputMode: "camera",
        imgFiles: imageFiles
          .filter((img) => img.status === "success" && img.file !== null)
          .map((img) => img.preview),
      });
    } else {
      if (type === "solution") {
        // 解答题：将第一个答案作为 subjectiveAnswer
        const subjectiveAnswer = [fillBlankViewModel.answers[0]?.value || ""];
        updateUserAnswer({
          inputMode: "keyboard",
          subjectiveAnswer,
        });
      } else if (type === "fill-blank") {
        // 🔧 修复：填空题使用 subjectiveAnswer 数组
        const subjectiveAnswer = fillBlankViewModel.answers.map(
          (answer) => answer.value
        );

        updateUserAnswer({
          inputMode: "keyboard",
          subjectiveAnswer,
        });
      } else if (type === "english-fill-blank") {
        updateUserAnswer({
          englishFillBlankAnswers: fillBlankViewModel.answers.map(
            (answer) => answer.value
          ),
        });
      }
    }
  }, [
    fillBlankViewModel.answers,
    inputMode,
    imageFiles,
    updateUserAnswer,
    type,
  ]);

  return (
    <div
      className={cn(
        "flex-1 overflow-y-auto overflow-x-hidden",
        !isReview ? "h-full" : "h-auto"
      )}
    >
      <div className="flex h-full flex-col">
        {/* 输入模式切换 */}
        <div className="mt-2 flex items-center justify-center gap-2">
          {!isReview && (
            <div className="font-resource-han-rounded flex-1 justify-start whitespace-nowrap text-[14px] font-normal leading-relaxed text-zinc-800/70">
              请认真作答，老师会检查作答结果
            </div>
          )}
          {!isReview && !isEnglishFillBlank && (
            <>
              <div className="relative flex h-8 w-[180px] items-center gap-2 rounded-lg bg-[rgba(31,29,27,0.05)] p-1">
                {/* 滑块动画 */}
                <div
                  className="absolute bottom-1 left-1 right-1 top-1 w-[82px] rounded-md bg-white shadow transition-all duration-300"
                  style={{
                    transform:
                      inputMode === "keyboard"
                        ? "translateX(0%)"
                        : "translateX(110%)",
                  }}
                />
                {/* 键盘 tab */}
                <div
                  className={cn(
                    "relative z-10 flex h-8 w-1/2 cursor-pointer items-center justify-center rounded-md transition-colors duration-300",
                    inputMode === "keyboard"
                      ? "text-[rgba(51,48,45,0.85)]"
                      : "text-[rgba(51,48,45,0.55)]"
                  )}
                  onClick={() => handleModeChange("keyboard")}
                >
                  <Keyboard className="mr-1 h-4 w-4" />
                  <span className="text-xs">键盘输入</span>
                </div>
                {/* 拍照 tab */}
                <div
                  className={cn(
                    "relative z-10 flex h-8 w-1/2 cursor-pointer items-center justify-center rounded-md transition-colors duration-300",
                    inputMode === "camera"
                      ? "text-[rgba(51,48,45,0.85)]"
                      : "text-[rgba(51,48,45,0.55)]"
                  )}
                  onClick={() => handleModeChange("camera")}
                >
                  <Camera className="mr-1 h-4 w-4" />
                  <span className="text-xs">拍照作答</span>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Tab 切换 */}
        {(type === "fill-blank" || type === "english-fill-blank") &&
          answers.length > 1 && (
            <div className="pb-13 mt-5 h-8 overflow-hidden">
              <ul className="flex overflow-x-auto pb-10">
                {answers.map((answer, index) => {
                  let tabColor = "";
                  if (isReview && selfEvaluation[index]) {
                    const evalResult = selfEvaluation[index];
                    if (evalResult === "right")
                      tabColor =
                        "bg-[rgba(132,214,75,0.15)] text-[#449908] border-[#84D64B]";
                    else if (evalResult === "partial")
                      tabColor =
                        "bg-[rgba(255,212,102,0.15)] text-[#CC6204] border-[#FFD466]";
                    else if (evalResult === "wrong")
                      tabColor =
                        "bg-[rgba(255,123,89,0.15)] text-[#D1320A] border-[#FF6139]";

                    if (activeBlankIndex === index) {
                      if (evalResult === "right")
                        tabColor = "bg-[#84D64B] text-white border-[#84D64B]";
                      else if (evalResult === "partial")
                        tabColor = "bg-[#FFD466] text-white border-[#FFD466]";
                      else if (evalResult === "wrong")
                        tabColor = "bg-[#FF6139] text-white border-[#FF6139]";
                    }
                  }
                  return (
                    <li
                      key={answer.id}
                      ref={(el) => {
                        tabRefs.current[index] = el;
                      }}
                      className={cn(
                        "border-1 mr-6 flex h-8 w-8 shrink-0 cursor-pointer items-center justify-center rounded-full border-solid border-[rgba(88,196,250,0.5)] bg-white text-center text-sm text-[#0A8AC2]",
                        activeBlankIndex === index
                          ? "bg-[#58C4FA] text-white"
                          : answer.value
                            ? "bg-white text-[#0A8AC2]"
                            : "bg-[rgba(88,196,250,0.15)] text-[#0A8AC2]",
                        isReview ? tabColor : ""
                      )}
                      onClick={() => {
                        setActiveBlankIndex(index);
                        // handleOptionClick(); // 已移除，使用新的状态管理
                      }}
                    >
                      {index + 1}
                    </li>
                  );
                })}
              </ul>
            </div>
          )}
        {inputMode === "keyboard" ? (
          <KeyboardInput type={type} viewModel={fillBlankViewModel} />
        ) : (
          <>
            {!isReview && (
              <CameraInput
                imageFiles={
                  imageFiles.filter((img) => img.file !== null) as any
                }
                onRemoveImage={handleRemoveImage}
                onRetryUpload={handleRetryUpload}
                onTriggerNativeUpload={triggerNativeImageUpload}
              />
            )}
          </>
        )}
        {/* 输入区域 */}
        <div className="flex-1">
          {isReview && (
            <>
              <div className="">
                <p className="text-[1.0625rem] font-bold text-[rgba(51,48,45,0.95)]">
                  我的答案：
                </p>
                {inputMode === "keyboard" ? (
                  <span className="text-[rgba(51,48,45,0.55)]">
                    {answers[activeBlankIndex]?.value || "（未作答）"}
                  </span>
                ) : (
                  <div className="mt-4 flex gap-2">
                    {imageFiles.filter((img) => img.status === "success")
                      .length === 0 ? (
                      <span className="text-[rgba(51,48,45,0.55)]">
                        （未上传图片）
                      </span>
                    ) : (
                      imageFiles
                        .filter((img) => img.status === "success")
                        .map((img) => (
                          <img
                            key={img.id}
                            src={img.preview}
                            alt="图片答案"
                            className="h-[100px] w-[100px] cursor-pointer rounded-lg border object-cover"
                          />
                        ))
                    )}
                  </div>
                )}
              </div>
              <div className="mt-4 flex">
                <span className="flex-shrink-0 text-[1.0625rem] font-bold text-[rgba(51,48,45,0.95)]">
                  核对答案：
                </span>
              </div>
              <div className="mt-4 rounded-xl border border-solid border-[rgba(88,196,250,0.15)] bg-[#E3F5FF] px-6 py-4">
                <span className="text-[#476275]">
                  <FormatMath
                    questionId={question?.questionId}
                    htmlContent={correctAnswers[activeBlankIndex] || ""}
                  />
                </span>
                {/* 自评结果区 */}
                {type !== "english-fill-blank" &&
                  (() => {
                    // 判断当前答案是否为空
                    const isCurrentAnswerEmpty =
                      inputMode === "keyboard"
                        ? !answers[activeBlankIndex]?.value?.trim()
                        : imageFiles.filter((img) => img.status === "success")
                            .length === 0;

                    // 🆕 按钮是否被锁定：空答案 或 自评已提交
                    const isButtonLocked =
                      isCurrentAnswerEmpty || isSelfEvaluationSubmitted || isEnglishFillBlank;

                    return (
                      <div className="self-evaluation-buttons mt-4 flex gap-4">
                        <button
                          className={cn(
                            "flex h-8 w-[5.75rem] items-center justify-center rounded-[9px] border shadow-[0_2px_0_0_rgba(51,46,41,0.12)] transition-all",
                            selfEvaluation[activeBlankIndex] === "right"
                              ? "border-[#84D64B] bg-[#EDF9E4] text-[#449908] shadow-[0_2px_0_0_rgba(132,214,75,0.6)]"
                              : isButtonLocked
                                ? "cursor-not-allowed border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)]"
                                : "cursor-pointer border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)] hover:bg-[rgba(51,46,41,0.02)]"
                          )}
                          disabled={isButtonLocked}
                          onClick={() =>
                            !isButtonLocked &&
                            viewModel.handleSelfEvaluate("right")
                          }
                        >
                          <span
                            className={cn(
                              "ml-1 text-xs",
                              isButtonLocked &&
                                selfEvaluation[activeBlankIndex] !== "right"
                                ? "opacity-30"
                                : ""
                            )}
                          >
                            {selfEvaluation[activeBlankIndex] === "right" ? (
                              <RightIconGreenSvg />
                            ) : (
                              <RightIconNormalSvg />
                            )}
                          </span>
                          <span
                            className={cn(
                              "ml-1 text-xs",
                              isButtonLocked &&
                                selfEvaluation[activeBlankIndex] !== "right"
                                ? "opacity-30"
                                : ""
                            )}
                          >
                            我答对了
                          </span>
                        </button>
                        <button
                          className={cn(
                            "flex h-8 w-[5.75rem] items-center justify-center rounded-[9px] border shadow-[0_2px_0_0_rgba(51,46,41,0.12)] transition-all",
                            selfEvaluation[activeBlankIndex] === "partial"
                              ? "border-[#FF7B59] bg-[#FFEBE6] text-[#D1320A] shadow-[0_2px_0_0_rgba(255,123,89,0.6)]"
                              : isButtonLocked
                                ? "cursor-not-allowed border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)]"
                                : "cursor-pointer border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)] hover:bg-[rgba(51,46,41,0.02)]"
                          )}
                          disabled={isButtonLocked}
                          onClick={() =>
                            !isButtonLocked &&
                            viewModel.handleSelfEvaluate("partial")
                          }
                        >
                          <span
                            className={cn(
                              "ml-1 text-xs",
                              isButtonLocked &&
                                selfEvaluation[activeBlankIndex] !== "partial"
                                ? "opacity-30"
                                : ""
                            )}
                          >
                            {selfEvaluation[activeBlankIndex] === "partial" ? (
                              <HalfRightIconRedSvg />
                            ) : (
                              <HalfRightIconNormalSvg />
                            )}
                          </span>
                          <span
                            className={cn(
                              "ml-1 text-xs",
                              isButtonLocked &&
                                selfEvaluation[activeBlankIndex] !== "partial"
                                ? "opacity-30"
                                : ""
                            )}
                          >
                            部分答对
                          </span>
                        </button>
                        <button
                          className={cn(
                            "flex h-8 w-[5.75rem] items-center justify-center rounded-[9px] border shadow-[0_2px_0_0_rgba(51,46,41,0.12)] transition-all",
                            selfEvaluation[activeBlankIndex] === "wrong"
                              ? "border-[#FF7B59] bg-[#FFEBE6] text-[#D1320A] shadow-[0_2px_0_0_rgba(255,123,89,0.6)]"
                              : isSelfEvaluationSubmitted
                                ? "cursor-not-allowed border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)]"
                                : "cursor-pointer border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)] hover:bg-[rgba(51,46,41,0.02)]"
                          )}
                          // 🆕 只有自评已提交时才禁用，允许用户在空答案时点击我答错了
                          disabled={isSelfEvaluationSubmitted}
                          onClick={() =>
                            !isSelfEvaluationSubmitted &&
                            viewModel.handleSelfEvaluate("wrong")
                          }
                        >
                          <span
                            className={cn(
                              "",
                              isButtonLocked &&
                                selfEvaluation[activeBlankIndex] !== "wrong"
                                ? "opacity-30"
                                : ""
                            )}
                          >
                            {selfEvaluation[activeBlankIndex] === "wrong" ? (
                              <WrongIconRedSvg />
                            ) : (
                              <WrongIconNormalSvg />
                            )}
                          </span>
                          <span
                            className={cn(
                              "ml-1 text-xs",
                              isButtonLocked &&
                                selfEvaluation[activeBlankIndex] !== "wrong"
                                ? "opacity-30"
                                : ""
                            )}
                          >
                            我答错了
                          </span>
                        </button>
                      </div>
                    );
                  })()}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

import { Signal } from "@preact-signals/safe-react";
import { FC, ReactNode, useMemo } from "react";
import { createPortal } from "react-dom";

export interface ContextMenuItem {
  icon?: ReactNode;
  name: string;
}
export const ContextMenu: FC<{
  menu: ContextMenuItem[];
  onItemClick(index: ContextMenuItem): void;
  port: Signal<HTMLDivElement | null>;
  pos: { x: number; y: number };
}> = ({ menu, onItemClick, port, pos }) => {
  const finalPos = useMemo(() => {
    if (!port.value) return { x: 0, y: 0 };
    const { left, top } = port.value.getBoundingClientRect();
    const x = pos.x - left;
    const y = pos.y - top - 15;
    return { x, y };
  }, [port.value, pos]);
  if (!port.value) return null;
  return createPortal(
    <div
      className="z-200 before:-translate-1/2 absolute flex w-max -translate-x-1/2 -translate-y-full items-center divide-x divide-white rounded-lg bg-[#797776] px-2 before:absolute before:-bottom-[8.5px] before:left-1/2 before:h-0 before:w-0 before:border-[6px_6px_0] before:border-b-transparent before:border-l-transparent before:border-r-transparent before:border-t-[#797776]"
      style={{
        top: finalPos.y,
        left: finalPos.x,
      }}
    >
      {menu.map((item, index) => (
        <div
          key={index}
          className="flex items-center justify-center gap-0.5 p-2 text-xs text-white"
          onPointerDown={(e) => {
            e.preventDefault();
            onItemClick(item);
          }}
        >
          {item.icon}
          {item.name}
        </div>
      ))}
    </div>,
    port.value
  );
};

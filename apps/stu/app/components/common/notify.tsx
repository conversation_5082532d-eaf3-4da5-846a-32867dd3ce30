"use client";

import { Signal, signal, useSignal } from "@preact-signals/safe-react";
import { useCallback, useMemo } from "@preact-signals/safe-react/react";
import { AnimatePresence, motion } from "motion/react";
import { FC } from "react";
import { createRoot } from "react-dom/client";
import { v4 as uuid } from "uuid";
interface Methods {
  show(message: string): void;
  error(message: string): void;
}

const Container: FC<{ methodsRef: Signal<Methods | null> }> = ({
  methodsRef,
}) => {
  const messages = useSignal<
    {
      id: string;
      message: string;
      color: string;
    }[]
  >([]);
  const show = useCallback(
    (message: string, color = "var(--color-text-2)") => {
      const id = uuid();
      messages.value = [
        ...messages.value,
        {
          id,
          message,
          color,
        },
      ];
      setTimeout(() => {
        messages.value = messages.value.filter((opt) => opt.id !== id);
      }, 3000);
    },
    [messages]
  );
  const error = useCallback(
    (message: string) => show(message, "var(--color-dim-red)"),
    [show]
  );
  const methods = useMemo(
    () => ({
      show,
      error,
    }),
    [error, show]
  );
  if (!methodsRef.value) methodsRef.value = methods;
  return (
    <div className="z-200 pointer-events-none fixed left-1/2 top-8 flex -translate-x-1/2 flex-col items-center gap-2">
      <AnimatePresence>
        {messages.value.map(({ message, color, id }) => (
          <motion.div
            className="min-w-26 rounded-full bg-white p-4 text-center shadow-[0px_4px_8px_0px_rgba(64,43,26,0.03)]"
            initial={{
              opacity: 0,
              y: -20,
            }}
            animate={{
              opacity: 1,
              y: 0,
            }}
            exit={{
              opacity: 0,
              y: -20,
            }}
            key={id}
            style={{ color }}
          >
            {message}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

const ref: Signal<Methods | null> = signal(null);
if (typeof window !== "undefined" && document && !ref.value) {
  const container = document.createElement("div");
  container.id = "notify-container";
  document.body.append(container);
  const root = createRoot(container);
  root.render(<Container methodsRef={ref} />);
}

export const notify = {
  show: (message: string) => {
    ref.value?.show(message);
  },
  error: (message: string) => {
    ref.value?.error(message);
  },
};

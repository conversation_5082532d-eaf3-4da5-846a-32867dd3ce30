// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { getStudentUserInfo } from "@repo/core/utils/stu/device";
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: "https://<EMAIL>/2",

  beforeSend(event, hint) {
    // 过滤掉 base64 编码的附件和大型二进制数据
    if (hint.attachments) {
      hint.attachments = hint.attachments.filter((att) => {
        if (typeof att.data === "object") return false;
        if (typeof att.data === "string" && att.data.includes("base64"))
          return false;
        return true;
      });
    }

    if (event.breadcrumbs)

    // 限制堆栈信息
    if (event.exception && event.exception.values) {
      event.exception.values = event.exception.values.map((exception) => {
        if (exception.stacktrace) {
          exception.stacktrace.frames = exception.stacktrace.frames?.slice(-20).map(frame => {
            if (frame.abs_path?.includes("base64")) {
              frame.abs_path = "[Large file removed]";
            }
            return frame;
          });
        }
        // 限制异常值的大小
        if (exception.value && exception.value.length > 1000) {
          exception.value = exception.value.substring(0, 1000);
        }
        return exception;
      });
    }

    // 限制消息内容
    if (event.message && event.message.length > 1000) {
      event.message = event.message.substring(0, 1000);
    }

    // 限制breadcrumbs数量和大小
    if (event.breadcrumbs && event.breadcrumbs.length > 20) {
      event.breadcrumbs = event.breadcrumbs.slice(-20); // 保留最后20个breadcrumbs
    }

    // 限制 event.extra 中的大数据
    if (event.extra) {
      Object.keys(event.extra).forEach((key) => {
        const value = event.extra![key];
        if (typeof value === "string" && value.length > 1000) {
          event.extra![key] = value.substring(0, 1000) + "...[truncated]";
        } else if (typeof value === "object" && value !== null) {
          // 检查对象是否包含 base64 数据
          const stringified = JSON.stringify(value);
          if (stringified.includes("base64") || stringified.length > 5000) {
            event.extra![key] = "[Large object removed]";
          }
        }
      });
    }

    // 限制 event.contexts 中的大数据
    if (event.contexts) {
      Object.keys(event.contexts).forEach((key) => {
        const context = event.contexts![key];
        if (context && typeof context === "object") {
          const stringified = JSON.stringify(context);
          if (stringified.length > 5000) {
            event.contexts![key] = {
              ...context,
              data: "[Large context data removed]",
            };
          }
        }
      });
    }

    // 限制请求数据大小
    if (event.request?.data) {
      const data = event.request.data;
      if (typeof data === "string" && data.length > 2000) {
        event.request.data = data.substring(0, 2000) + "...[truncated]";
      } else if (typeof data === "object") {
        const stringified = JSON.stringify(data);
        if (stringified.length > 2000) {
          event.request.data = "[Large request data removed]";
        }
      }
    }

    // 检查整个事件的大小，如果过大则拒绝发送
    try {
      const eventSize = JSON.stringify(event).length;
      if (eventSize > 100000) {
        // 100KB 限制
        console.warn("Sentry event too large, rejecting:", eventSize, "bytes");
        return null; // 拒绝发送
      }
    } catch (error) {
      console.warn("Failed to calculate event size:", error);
    }

    return event;
  },

  // Add optional integrations for additional features
  integrations: [
    Sentry.globalHandlersIntegration(),
    Sentry.browserTracingIntegration(),
    // 降低性能分析的采样率
    Sentry.browserProfilingIntegration(),
    // 优化Session Replay配置以减少数据量
    Sentry.replayIntegration({
      maskAllInputs: true, // 遮蔽输入内容减少数据量
      maskAllText: false, // 遮蔽文本内容减少数据量
      blockAllMedia: true, // 阻止媒体文件录制
    }),
  ],
  tracePropagationTargets: [/^https:\/\/(.*)(xiaoluxue\.cn|xiaoluxue\.com)/],

  // 大幅降低采样率以减少数据量
  profileSessionSampleRate: 0.1,
  profilesSampleRate: 1.0, // 从1.0降到0.1
  tracesSampleRate: 1.0, // 从1.0降到0.1

  // Session Replay采样率 - 大幅降低以减少数据量
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 0.1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,
});

const user = getStudentUserInfo();
if (user) {
  const { userId: id, userName: username, ...rest } = user;
  Sentry.setUser({
    id,
    username,
    ...rest,
  });
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;

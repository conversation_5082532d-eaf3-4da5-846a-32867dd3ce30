// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { getStudentUserInfo } from "@repo/core/utils/stu/device";
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: "https://<EMAIL>/2",

  beforeSend(event, hint) {
    // 过滤掉 base64 编码的附件和大型二进制数据
    if (hint.attachments) {
      hint.attachments = hint.attachments.filter((att) => {
        if (typeof att.data === "object") return false;
        if (typeof att.data === "string" && att.data.includes("base64"))
          return false;
        return true;
      });
    }

    // 限制堆栈信息
    if (event.exception && event.exception.values) {
      event.exception.values = event.exception.values.map((exception) => {
        if (exception.stacktrace) {
          exception.stacktrace.frames = exception.stacktrace.frames?.slice(-20);
        }
        // 限制异常值的大小
        if (exception.value && exception.value.length > 1000) {
          exception.value = exception.value.substring(0, 1000);
        }
        return exception;
      });
    }

    // 限制消息内容
    if (event.message && event.message.length > 1000) {
      event.message = event.message.substring(0, 1000);
    }

    // 限制breadcrumbs数量和大小
    if (event.breadcrumbs && event.breadcrumbs.length > 20) {
      event.breadcrumbs = event.breadcrumbs.slice(-20); // 保留最后20个breadcrumbs
    }

    return event;
  },

  // Add optional integrations for additional features
  integrations: [
    Sentry.globalHandlersIntegration(),
    Sentry.browserTracingIntegration(),
    // 降低性能分析的采样率
    Sentry.browserProfilingIntegration(),
    // 优化Session Replay配置以减少数据量
    Sentry.replayIntegration({
      maskAllInputs: true, // 遮蔽输入内容减少数据量
      maskAllText: false, // 遮蔽文本内容减少数据量
      blockAllMedia: true, // 阻止媒体文件录制
    }),
  ],
  tracePropagationTargets: [/^https:\/\/(.*)(xiaoluxue\.cn|xiaoluxue\.com)/],

  // 大幅降低采样率以减少数据量
  profileSessionSampleRate: 0.1,
  profilesSampleRate: 1.0, // 从1.0降到0.1
  tracesSampleRate: 1.0, // 从1.0降到0.1

  // Session Replay采样率 - 大幅降低以减少数据量
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 0.1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,
});

const user = getStudentUserInfo();
if (user) {
  const { userId: id, userName: username, ...rest } = user;
  Sentry.setUser({
    id,
    username,
    ...rest,
  });
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;

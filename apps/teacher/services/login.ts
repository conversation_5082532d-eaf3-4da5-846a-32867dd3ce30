import { PLATFORM } from "@/enums";
import { r } from "@/libs";
import { LoginResponse, School } from "@/types";

const NEXT_PUBLIC_UCENTER_API_URL =
  process.env.NEXT_PUBLIC_UCENTER_API_URL || "";

/**
 * 图形校验短信验证码发送
 */
export const sendSmsWithCaptcha = (data: {
  captchaVerifyParam: string;
  phone_number: string;
  platform: PLATFORM;
  sceneId: string;
}) => {
  return r.post(
    `${NEXT_PUBLIC_UCENTER_API_URL}/api/v1/sms/sendWithCaptcha`,
    {
      ...data,
      business_type: "login",
    },
    {
      showToast: false,
    }
  );
};

/**
 * 教师身份验证
 */
export const verifyTeacher = (data: {
  phoneNumber: string;
  verificationCode: string;
}) => {
  return r.post<{
    isVerified: boolean;
    schools: School[];
  }>(`${NEXT_PUBLIC_UCENTER_API_URL}/api/v1/access/teacher/verify`, data, {
    showToast: false,
  });
};

/**
 * 教师登录
 */
export const login = (userPhone: string, verifyCode: string) => {
  return r.post<LoginResponse>(
    `${NEXT_PUBLIC_UCENTER_API_URL}/api/v1/access/login`,
    {
      // 登录方式 => 1为手机验证码 2为账号密码
      loginTypeId: 1,
      // 登录端 => 1为学生端 2为教师端
      platformId: 2,
      userPhone,
      verifyCode,
    },
    {
      showToast: false,
    }
  );
};

/**
 * 通过 Next.js API Route 设置 cookie
 * @deprecated
 */
export const setCookieWithApiRoute = (token: string) => {
  return fetch("/api/login/cookie", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ token }),
  }).then((res) => res.json());
};

/**
 * 通过 Next.js API Route 清除 cookie
 * @deprecated
 */
export const clearCookieWithApiRoute = () => {
  return fetch("/api/login/cookie", {
    method: "DELETE",
  }).then((res) => res.json());
};

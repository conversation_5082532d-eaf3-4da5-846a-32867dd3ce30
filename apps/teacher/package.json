{"name": "teacher", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3030", "start": "next start --port 3030", "build": "next build", "build:dev": "NODE_ENV=development next build && node scripts/static-upload.js", "build:test": "NODE_ENV=test next build && node scripts/static-upload.js", "build:prod": "NODE_ENV=production next build && node scripts/static-upload.js", "lint": "next lint", "build:local": "NODE_ENV=local next build", "deploy:dev": "NODE_ENV=production next build && node scripts/static-upload.js && { pm2 del schroolroom-tch || true; pm2 start -n schroolroom-tch pnpm -- run start; }"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@preact-signals/safe-react": "^0.9.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@repo/core": "workspace:*", "@repo/ui": "workspace:*", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.6", "@types/marked": "^6.0.0", "@use-gesture/react": "^10.3.1", "@xyflow/react": "^12.8.1", "ahooks": "^3.8.4", "antd": "^5.24.7", "antd-mobile": "^5.39.0", "await-to-js": "^3.0.0", "axios": "^1.8.4", "bowser": "^2.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "colord": "^2.9.3", "copy-text-to-clipboard": "^3.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.4.2", "html-to-image": "^1.11.13", "immer": "^10.1.1", "katex": "^0.16.22", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "marked": "^15.0.8", "mitt": "^3.0.1", "next": "15.3.1", "ramda": "^0.30.1", "react": "catalog:react19", "react-day-picker": "8.10.1", "react-dom": "catalog:react19", "react-photo-view": "^1.2.7", "react-virtuoso": "^4.12.7", "react-zoom-pan-pinch": "^3.7.0", "sanitize-html": "^2.17.0", "store2": "^2.14.4", "tailwind-merge": "^3.3.0", "ts-pattern": "^5.7.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^20.17.30", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@types/sanitize-html": "^2.16.0", "ali-oss": "^6.22.0", "eslint-config-next": "15.3.1", "tailwindcss": "^4.1.8", "tailwind-motion": "^0.0.1", "tw-animate-css": "^1.2.5", "typescript": "^5.8.3", "vconsole": "^3.15.1"}}
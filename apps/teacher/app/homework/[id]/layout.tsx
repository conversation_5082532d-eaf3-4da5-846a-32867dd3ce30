"use client";

import { useApp } from "@/hooks/useApp";
import { getStorageSync, removeStorageAsync } from "@/utils/storage-utils";
import { useRouter } from "next/navigation";
import { memo, use, useCallback, useEffect, useState } from "react";
import Header from "./_components/layout/Header";
import TabNav from "./_components/layout/TabNav";
import { HomeworkSettingsProvider } from "./_context/homework-settings-context";
import { TaskProvider, useTaskContext } from "./_context/task-context";

// 使用memo包装组件避免不必要的重渲染
const MemoizedHeader = memo(Header);
const MemoizedTabNav = memo(TabNav);

// 创建内部组件以使用context
function HomeworkLayout({
  children,
  taskId,
}: {
  children: React.ReactNode;
  taskId: string;
}) {
  const { setTaskId } = useTaskContext();

  // 在组件挂载时设置任务ID
  useEffect(() => {
    setTaskId(taskId);
  }, [taskId, setTaskId]);

  const router = useRouter();
  const { setOpen } = useApp();
  const [source, setSource] = useState<string | null>(null);

  // 获取来源参数 - 从 URL 和 localStorage 获取
  useEffect(() => {
    // 首先检查 URL 中的 source 参数
    const urlParams = new URLSearchParams(window.location.search);
    const urlSource = urlParams.get("source");

    if (urlSource) {
      // URL 中有 source 参数，优先使用
      setSource(urlSource);
    } else {
      // URL 中没有，尝试从 localStorage 获取
      const storedSource = getStorageSync<string>("homework_source");
      if (storedSource) {
        setSource(storedSource);
      }
    }
  }, []);

  // 使用useCallback优化处理函数
  // 使用useCallback优化处理函数
  const handleBack = useCallback(() => {
    // 保存当前的courseId到localStorage，以便在返回后恢复
    const currentParams = new URLSearchParams(window.location.search);
    const courseId = currentParams.get("courseId");
    if (courseId) {
      // 异步保存，不阻塞导航
      requestIdleCallback(() => {
        try {
          localStorage.setItem("homework_last_course_id", courseId);
        } catch (error) {
          console.error("Failed to save courseId to localStorage:", error);
        }
      });
    }

    // 根据来源决定返回路径
    // 如果来源是 assign，返回作业列表时保持来源状态
    if (source === "assign") {
      router.push("/homework?source=assign");
      // 来源是 assign 时，不设置侧边栏展开，让作业列表页自己处理
    } else {
      localStorage.setItem("homework_source", "courseId");
      router.push("/homework");
      // 只有非 assign 来源时才展开侧边栏
      setTimeout(() => {
        setOpen(true);
      }, 100);
    }
  }, [setOpen, router, source]);

  // 离开页面清理homework_currentTask
  useEffect(() => {
    return () => {
      setTaskId(0);
      removeStorageAsync("homework_currentTask").catch((error) => {
        console.error("Failed to remove homework_currentTask:", error);
      });
    };
  }, [setTaskId]);
  return (
    <div className="flex h-full flex-col pb-3">
      {/* 组合头部区域：头部和标签导航 */}
      <div className="z-0 shrink-0 px-6">
        <MemoizedHeader onBack={handleBack} />
        <MemoizedTabNav />
      </div>
      {/* 内容区域 */}
      <div className="flex-1 will-change-transform">{children}</div>
    </div>
  );
}

// 主布局组件
export default function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);

  return (
    <TaskProvider>
      <HomeworkSettingsProvider>
        <HomeworkLayout taskId={id}>{children}</HomeworkLayout>
      </HomeworkSettingsProvider>
    </TaskProvider>
  );
}

"use client";

import {
  getHomeworkDetail,
  GetHomeworkDetailParams,
} from "@/services/homework";
import { Badge } from "@/ui/badge";
import { <PERSON><PERSON> } from "@/ui/button";
import { toast } from "@/ui/toast";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { DataTable, TableColumn } from "../../../../_components/DataTable";
import {
  activeTabSignal,
  useTaskContext,
} from "../../../_context/task-context";
import { StudentRemindDrawer } from "./components/student-remind-drawer";
import { StudentSection } from "./components/student-section";
// import ThumbsUpGif from "@/public/assign/thumbs-up.gif";
// import BellIcon from "@/public/assign/bell.svg";
import { InputSearch } from "@/ui/searchInput";
import ClassStats from "./components/class-stats";
// import ExportIcon from "@/public/icons/ic_export.svg";
import { cn } from "@/utils/utils";
import { useStudentRemind } from "./hooks/useStudentRemind";
// import Image from "next/image";
import { QUESTION_DIFFICULT, questionDifficultEnumManager } from "@/enums";
import { useApp } from "@/hooks";
import { useUmeng } from "@/hooks/useUmeng";
import { StudentDetailV2 } from "@/types/homeWork";
import { ScrollArea } from "@/ui/scroll-area";
import Avatar from "@/ui/tch-avatar";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { SortingState } from "@tanstack/react-table";
import { useMount, useUpdateEffect } from "ahooks";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useHomeworkSettings } from "../../../_context/homework-settings-context";
import { ThumbsUpButton } from "../../thumbs-up-button";
// import { handleStudentBehaviorV2, StudentBehaviorType } from "@/services";
import { StudentBase } from "@/types/homeWork";

const ExportIconNew = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.66671 4.00016C2.66671 3.26378 3.26366 2.66683 4.00004 2.66683H5.33156C5.69975 2.66683 5.99823 2.36835 5.99823 2.00016C5.99823 1.63197 5.69975 1.3335 5.33156 1.3335H4.00004C2.52728 1.3335 1.33337 2.5274 1.33337 4.00016V12.0002C1.33337 13.4729 2.52728 14.6668 4.00004 14.6668H12C13.4728 14.6668 14.6667 13.4729 14.6667 12.0002V4.00016C14.6667 2.5274 13.4728 1.3335 12 1.3335H10.6727C10.3045 1.3335 10.006 1.63197 10.006 2.00016C10.006 2.36835 10.3045 2.66683 10.6727 2.66683H12C12.7364 2.66683 13.3334 3.26378 13.3334 4.00016V12.0002C13.3334 12.7365 12.7364 13.3335 12 13.3335H4.00004C3.26366 13.3335 2.66671 12.7365 2.66671 12.0002V4.00016ZM7.97074 1.34294C8.33893 1.34294 8.63741 1.64141 8.63741 2.0096L8.63741 8.0096L8.63732 8.02066L9.52278 7.1352C9.78313 6.87485 10.2052 6.87485 10.4656 7.1352C10.7259 7.39555 10.7259 7.81766 10.4656 8.07801L8.46559 10.078C8.20524 10.3384 7.78313 10.3384 7.52278 10.078L5.52278 8.07801C5.26243 7.81766 5.26243 7.39555 5.52278 7.1352C5.78313 6.87485 6.20524 6.87485 6.46559 7.1352L7.30408 7.97369V2.0096C7.30408 1.64141 7.60255 1.34294 7.97074 1.34294Z"
      fill="currentColor"
    />
  </svg>
);

const BellIconNew = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.88558 12.1071C9.88536 12.9316 9.22059 13.6002 8.40023 13.6002C7.57983 13.6002 6.9151 12.9316 6.91488 12.1071H9.88558ZM8.40023 2.40002C10.4524 2.40009 11.867 3.77132 11.867 5.76038V6.87952C11.867 7.4395 11.9496 7.7454 12.2645 7.99963C12.302 8.02987 12.4691 8.15435 12.5164 8.19299C13.238 8.77592 13.6016 9.33386 13.6004 10.2399C13.5998 10.8469 13.082 11.3617 12.4442 11.36H4.35531C3.71588 11.3659 3.20465 10.8434 3.20003 10.2399C3.19371 9.32732 3.55247 8.7786 4.28304 8.19299C4.32869 8.15659 4.48199 8.02875 4.51839 7.99963C4.83989 7.74211 4.93343 7.43928 4.93343 6.87952V5.76038C4.93343 3.77127 6.34798 2.40002 8.40023 2.40002Z"
      fill="currentColor"
    />
  </svg>
);

export default function Report() {
  // url中获取assignId
  const searchParams = useSearchParams();
  const assignId = searchParams.get("assignId");
  const { getRoleSummary } = useApp();
  // 使用signal管理状态
  const searchTermSignal = useSignal("");
  const selectedStudentIdSignal = useSignal<number | null>(null);
  const selectedStudentSignal = useSignal<StudentBase | null>(null);
  const drawerOpenSignal = useSignal(false);
  // 添加排序状态
  const sortBySignal = useSignal<
    | "studyScore"
    | "progress"
    | "accuracyRate"
    | "answerCount"
    | "costTime"
    | undefined
  >(undefined);
  const sortTypeSignal = useSignal<"asc" | "desc" | undefined>(undefined);

  // 使用报告上下文
  const {
    classData,
    loading,
    taskId,
    setActiveTab,
    currentCourse,
    homeworkData,
    studentData,
    // studentListMap,
    getStudentName,
    getStudentAvatar,
    taskData,
    setStudentListMap,
    setStudentId,
    setStudentName,
    updateStudentData,
    setViewMode,
    useFetchHomeworkDetailRequest,
    assignId: assignIdSignal,
  } = useTaskContext();
  useUmeng(UmengCategory.HOMEWORK, "homework_list_report_student_tab");
  assignIdSignal.value = assignId || "";
  // 获取用户信息
  const { useBatchPraise, useBatchRemind } = useStudentRemind();
  const [isInit, setIsInit] = useState(false);
  const [taskType, setTaskType] = useState<"praise" | "attention">("attention");
  // const [classStats, setClassStats] = useState<{
  //   classProgress: string;
  //   averageAccuracy: string;
  //   averageTime: string;
  // }>({
  //   classProgress: "0",
  //   averageAccuracy: "0",
  //   averageTime: "0 分钟",
  // });
  // 获取作业设置
  const { settings } = useHomeworkSettings();
  const settingsValue = useComputed(() => settings.value);

  const praiseList = useComputed(() => {
    // const list =
    //   homeworkData.value?.detail?.praiseList?.map(
    //     (studentId) => studentListMap.value?.[studentId]
    //   ) || [];
    const list =
      homeworkData.value?.detail?.praiseStudents?.filter(
        (student) => (student as any).praiseCount < 1
      ) || [];

    return list;
  });
  useEffect(() => {
    if (praiseList.value.length > 0) {
      // DONE: 埋点15 => `homework_list_report_active_label_show` 作业报告页中展示建议鼓励名单 需要list中有数据
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.REPORT_ACTIVE_LABEL_SHOW,
        {
          total: praiseList.value.length,
        }
      );
    }
  }, [praiseList.value]);
  const attentionList = useComputed(() => {
    // const list =
    //   homeworkData.value?.detail?.attentionList?.map(
    //     (studentId) => studentListMap.value?.[studentId]
    //   ) || [];
    const list =
      homeworkData.value?.detail?.attentionStudents?.filter(
        (student) => (student as any).attentionCount < 1
      ) || [];
    return list;
  });
  useEffect(() => {
    if (attentionList.value.length > 0) {
      // DONE: 埋点16 => `homework_list_report_negtive_label_show` 作业报告页中展示建议关注名单 需要list中有数据
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.REPORT_NEGATIVE_LABEL_SHOW,
        {
          total: attentionList.value.length,
        }
      );
    }
  }, [attentionList.value]);
  // const {
  //   data,
  //   loading: pollingLoading,
  //   run,
  //   cancel,
  // } = useRequest(
  //   async () => {
  //     if (!classData.value.classId || !assignId || !taskId.value) {
  //       // toast.error("获取作业详情失败，请重新进入");
  //       // router.back();
  //       return;
  //     }

  //     const params: GetHomeworkDetailParams = {
  //       taskId: taskId.value,
  //       assignId: assignId ? Number(assignId) : 0, // 作业ID
  //       resourceId: classData.value.contentId
  //         ? Number(classData.value.contentId.replace("content-", ""))
  //         : undefined,
  //     };
  //     return getHomeworkDetail(params);
  //   },
  //   {
  //     pollingInterval: 60000,
  //     pollingWhenHidden: false,
  //     manual: true,
  //     onError: (error) => {
  //       console.error("获取作业详情失败:", error);
  //       toast.error("获取作业详情失败");
  //     },
  //   }
  // );
  const {
    data,
    loading: pollingLoading,
    run,
    cancel,
  } = useFetchHomeworkDetailRequest();

  useUpdateEffect(() => {
    if (data) {
      homeworkData.value = data;
      setStudentListMap(data.detail.studentReports as any);
    }
  }, [data]);

  useUpdateEffect(() => {
    if (!isInit) {
      setIsInit(true);
      if (pollingLoading) {
        loading.value = true;
      } else {
        loading.value = false;
      }
    } else {
      loading.value = false;
    }
  }, [pollingLoading]);

  useMount(() => {
    loading.value = true;
  });

  useSignalEffect(() => {
    if (activeTabSignal.value === "report") {
      run();
    } else {
      cancel();
    }
  });

  // 获取作业详情数据
  const fetchHomeworkDetail = useCallback(
    async (silentLoading = false) => {
      if (!classData.value.classId || !assignId || !taskId.value) {
        // toast.error("获取作业详情失败，请重新进入");
        // router.back();
        return;
      }

      if (!silentLoading) {
        loading.value = true;
      }

      try {
        // 构建请求参数
        const params: GetHomeworkDetailParams = {
          taskId: taskId.value,
          assignId: assignId ? Number(assignId) : 0, // 作业ID
          resourceId: classData.value.contentId,
          // ? Number(classData.value.contentId.replace("content-", ""))
          // : undefined,
        };

        // 调用服务获取数据
        const data = await getHomeworkDetail(params);

        homeworkData.value = data;
        // 构建学生列表 map - 只在获取数据后设置一次
        setStudentListMap(data.detail.studentReports as any);
      } catch (error) {
        console.error("获取作业详情失败:", error);
        toast.error("获取作业详情失败");
      } finally {
        if (!silentLoading) {
          loading.value = false;
        }
      }
    },
    [
      classData.value,
      taskId,
      assignId,
      loading,
      homeworkData,
      setStudentListMap,
    ]
  );

  const { run: handleRunPraise } = useBatchPraise(
    (homeworkData.value?.detail.praiseStudents?.filter(
      (student) => (student as any).praiseCount < 1
    ) as StudentDetailV2[]) || [],
    () => setTimeout(() => fetchHomeworkDetail(), 1000)
  );
  const { run: handleRunRemind } = useBatchRemind(
    (homeworkData.value?.detail.attentionStudents?.filter(
      (student) => (student as any).attentionCount < 1
    ) as StudentDetailV2[]) || [],
    () => setTimeout(() => fetchHomeworkDetail(), 1000)
  );

  // 监听 classData 变化，重新获取数据
  useUpdateEffect(() => {
    if (classData.value.classId && taskId.value && isInit) {
      console.log(
        `[useEffect] classData.value.classId=${classData.value.classId}`,
        classData.value,
        taskData.value
      );

      fetchHomeworkDetail();
    }
  }, [classData.value, fetchHomeworkDetail, taskId.value, taskData.value]);

  // 班级统计数据
  // const classStats = {
  //   classProgress: homeworkData.value
  //     ? `${(homeworkData.value.detail.avgProgress * 100).toFixed(0)}`
  //     : "0",
  //   averageAccuracy: homeworkData.value
  //     ? `${(homeworkData.value.detail.avgAccuracy * 100).toFixed(1)}`
  //     : "0",
  //   averageTime: homeworkData.value
  //     ? `${homeworkData.value.detail.avgCostTime} 分钟`
  //     : "0 分钟",
  // };
  const classStat = useComputed(() => {
    return {
      classProgress: homeworkData.value
        ? `${(homeworkData.value.detail.avgProgress * 100).toFixed(0)}`
        : "0",
      // averageAccuracy: homeworkData.value
      //   ? `${(homeworkData.value?.detail.avgAccuracy * 100) % 1 === 0 ? (homeworkData.value.detail.avgAccuracy * 100).toFixed(0) : (homeworkData.value.detail.avgAccuracy * 100).toFixed(1)}`
      //   : "0",
      averageAccuracy: homeworkData.value
        ? `${(homeworkData.value.detail.avgAccuracy * 100).toFixed(0)}`
        : "0",
      averageTime: homeworkData.value
        ? `${homeworkData.value.detail.avgCostTime} 分钟`
        : "0 分钟",
    };
  });

  // 处理学生点击
  const handleStudentClick = (studentId: number, student: StudentBase) => {
    // DONE: 埋点19 => `homework_list_report_single_click` 作业报告页中点击名单中的某学生
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.REPORT_SINGLE_CLICK,
      {
        subject: taskData.value?.subject,
        job: getRoleSummary(),
      }
    );
    // console.log("studentClick", student);
    selectedStudentIdSignal.value = studentId;
    selectedStudentSignal.value = student;
    drawerOpenSignal.value = true;
    // cancel();
  };

  // 过滤学生列表（基于搜索词）
  const filteredStudents = useComputed(
    () =>
      homeworkData.value?.detail.studentReports?.filter((student) =>
        getStudentName(student.studentId).includes(searchTermSignal.value)
      ) || []
  );

  // 抽离查看学生详情的函数
  const handleViewStudent = useCallback(
    (studentId: number) => {
      // DONE: 埋点26 => homework_list_report_student_click 作业报告页中点击列表中的学生
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.REPORT_STUDENT_TABLE_VIEW_CLICK,
        {
          eventName: "作业报告页中点击列表中的学生",
        }
      );

      console.log(
        "[Report] Viewing student details:",
        studentId,
        getStudentName(studentId)
      );

      // 获取学生名称
      const studentName = getStudentName(studentId);

      // 使用 Context 提供的 setter 函数，而不是直接修改 signal 的 value 属性
      setStudentId(String(studentId));
      setStudentName(studentName);
      const studentReport = homeworkData.value?.detail.studentReports?.find(
        (student) => student.studentId === studentId
      );
      const studentPraise = homeworkData.value?.detail.praiseStudents?.find(
        (student) => student.studentId === studentId
      );
      // console.log("student class", classData.value);
      const studentAttention =
        homeworkData.value?.detail.attentionStudents?.find(
          (student) => student.studentId === studentId
        );
      if (studentReport || studentPraise || studentAttention) {
        updateStudentData({
          ...classData.value,
          accuracy: (studentReport as any)?.accuracy || 0,
          praiseCount: (studentPraise as any)?.praiseCount || 0,
          // attentionCount: (studentAttention as any)?.attentionCount,
          progress: (studentReport as any)?.progress || 0,
          studentId: String(studentId),
          studentName,
          publishTime: classData.value.publishTime,
          deadline: classData.value.deadline,
          praiseDefaultText: (studentPraise as any)?.praiseDefaultText,
          pushDefaultText:
            (studentAttention as any)?.pushDefaultText ||
            (studentPraise as any)?.pushDefaultText ||
            "",
          studentType: studentPraise
            ? "praise"
            : studentAttention
              ? "attention"
              : "other",
        });
      }
      // 确保在设置studentId和name之后再切换视图模式
      setViewMode("student");

      // 切换到答题结果标签页
      setActiveTab("results");

      // URL更新会由TabNav的useEffect自动处理，无需手动调用router
    },
    [getStudentName, setViewMode, setStudentId, setStudentName, setActiveTab]
  );

  useUpdateEffect(() => {
    if (studentData.value.studentId) {
      const studentId = Number(studentData.value.studentId);
      const studentName = getStudentName(studentId);

      // 使用 Context 提供的 setter 函数，而不是直接修改 signal 的 value 属性
      setStudentId(String(studentId));
      setStudentName(studentName);
      const studentReport = homeworkData.value?.detail.studentReports?.find(
        (student) => student.studentId === studentId
      );
      const studentPraise = homeworkData.value?.detail.praiseStudents?.find(
        (student) => student.studentId === studentId
      );
      const studentAttention =
        homeworkData.value?.detail.attentionStudents?.find(
          (student) => student.studentId === studentId
        );
      if (studentReport || studentPraise || studentAttention) {
        updateStudentData({
          accuracy: (studentReport as any)?.accuracy || 0,
          praiseCount: (studentPraise as any)?.praiseCount || 0,
          // attentionCount: (studentAttention as any)?.attentionCount,
          progress: (studentReport as any)?.progress || 0,
          studentId: String(studentId),
          studentName,
          publishTime: classData.value.publishTime,
          deadline: classData.value.deadline,
          // pushDefaultText: (studentAttention as any)?.pushDefaultText || (studentPraise as any).pushDefaultText || "",
        });
      }
    }
  }, [homeworkData.value]);

  // 处理排序变化
  const handleSortChange = useCallback(
    (field: string | undefined, direction: "asc" | "desc" | undefined) => {
      // 将字段名映射到API所需的排序字段
      let sortBy:
        | "studyScore"
        | "progress"
        | "accuracyRate"
        | "answerCount"
        | "costTime"
        | undefined = undefined;

      if (field) {
        // 根据列字段名映射为API所需的排序字段
        switch (field) {
          case "studyScore":
            sortBy = "studyScore";
            break;
          case "progress":
            sortBy = "progress";
            break;
          case "accuracyRate":
            sortBy = "accuracyRate";
            break;
          case "incorrectNum": // 映射为answerCount
            sortBy = "answerCount";
            break;
          case "costTime":
            sortBy = "costTime";
            break;
          default:
            sortBy = undefined;
        }
      }

      // 更新排序状态信号
      sortBySignal.value = sortBy;
      sortTypeSignal.value = direction;

      console.log(`排序变更: ${sortBy} ${direction}`);
    },
    [sortBySignal, sortTypeSignal]
  );

  // 定义表格列
  const columns: TableColumn<(typeof filteredStudents.value)[0]>[] = [
    {
      title: "学生",
      field: "studentId",
      fixed: "left",
      width: 5.5 * 16,
      hideInColumnSetting: true, // 学生列不出现在编辑表格中
      render: (_, record) => {
        return (
          <div className="flex w-full items-center gap-2">
            <Avatar
              src={getStudentAvatar(record.studentId)}
              alt={getStudentName(record.studentId) || ""}
              className="h-6 w-6 cursor-pointer rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                handleViewStudent(record.studentId);
              }}
            />
            <span className="whitespace-nowrap">
              {getStudentName(record.studentId)}
            </span>
            {/* <div className="flex items-center gap-1">
              {record.tags
                .filter((tag) => tag.type !== 0)
                .map(
                  (tag) =>
                    tag.label && (
                      <Badge
                        key={tag.label}
                        variant="outline"
                        className={cn(
                          "rounded-[0.1875rem] border-none px-1",
                          tag.type === 1
                            ? "bg-green-5 text-green-0"
                            : "bg-orange-5 text-orange-1",
                          tag.type === 0 && "text-gray-5 !bg-transparent",
                          "text-xs"
                        )}
                      >
                        {tag.label}
                      </Badge>
                    )
                )}
            </div> */}
          </div>
        );
      },
    },
    {
      title: "标签",
      field: "tags",
      align: "left",
      width: 5.5 * 10,
      render: (_, record) => {
        return (
          <div className="flex items-center gap-1">
            {record.tags
              .filter((tag) => tag.type !== 0)
              .map(
                (tag) =>
                  tag.label && (
                    <Badge
                      key={tag.label}
                      variant="outline"
                      className={cn(
                        "rounded-[0.1875rem] border-none px-1",
                        tag.type === 1
                          ? "bg-green-5 text-green-0"
                          : "bg-orange-5 text-orange-1",
                        tag.type === 0 && "text-gray-5 !bg-transparent",
                        "text-xs"
                      )}
                    >
                      {tag.label}
                    </Badge>
                  )
              )}
          </div>
        );
      },
    },
    // {
    //   sortable: true,
    //   title: "学习能量",
    //   field: "studyScore",
    //   align: "left",
    //   className: "font-medium",
    // },
    {
      title: "完成进度",
      field: "progress",
      align: "left",
      sortable: true,
      width: 5.5 * 16,
      render: (_, record) => (
        <div className="inline-flex items-center justify-center gap-2">
          <div className="bg-fill-gray-2 h-2 w-24 rounded-full">
            <div
              className={`bg-primary-2 h-2 rounded-full`}
              style={{ width: `${Math.ceil(record.progress * 100)}%` }}
            />
          </div>
          <span className={"text-gray-2"}>
            {Math.ceil(record.progress * 100)}%
          </span>
        </div>
      ),
    },
    {
      title: "正确率",
      field: "accuracyRate",
      align: "left",
      sortable: true,
      width: 5.5 * 16,
      // 向下取整不保留小数
      render: (value: number) => `${Math.floor(value * 100 || 0)}%`,
    },
    {
      title: "答题难度",
      field: "difficultyDegree",
      align: "left",
      width: 5.5 * 16,
      visible: false,
      render: (value) =>
        questionDifficultEnumManager.getLabelByValue(
          value as QUESTION_DIFFICULT
        ),
    },
    {
      title: "错题/答题",
      field: "incorrectNum",
      align: "left",
      sortable: true,
      width: 5.5 * 16,
      render: (_, record) => `${record.incorrectNum} / ${record.answerNum}`,
    },
    {
      title: "用时",
      field: "costTime",
      align: "left",
      width: 5.5 * 16,
      sortable: true,
      // x小时 x 分钟，不够1小时显示x分钟
      render: (val) => {
        const value = Number(val / 1000);
        const hours = Math.floor(value / 3600);
        const minutes = Math.floor((value % 3600) / 60);
        return hours > 0 ? `${hours}小时 ${minutes}分钟` : `${minutes}分钟`;
      },
    },
    {
      title: "操作",
      field: "operation",
      align: "center",
      fixed: "right",
      width: 3.75 * 16,
      render: (_, record) => (
        <Button
          variant="link"
          className="text-primary-1 h-auto cursor-pointer p-0"
          onClick={(e) => {
            e.stopPropagation();
            handleViewStudent(record.studentId);
          }}
        >
          查看
        </Button>
      ),
    },
  ];

  // 自定义工具栏
  const customToolbar = (
    <div className="flex w-full items-center justify-between gap-2">
      <div className="flex items-center gap-4">
        <div className="whitespace-nowrap text-base font-semibold leading-[150%] text-[#202131]">
          学生列表
        </div>

        <div className="relative w-full max-w-sm">
          <InputSearch
            placeholder="搜索学生姓名"
            className="h-7 w-[15rem]"
            debounce={true}
            wait={300}
            value={searchTermSignal.value}
            onChange={(e) => (searchTermSignal.value = e.target.value)}
          />
        </div>
        {/* <span className="text-sm text-gray-500 whitespace-nowrap">
          共 {homeworkData.value?.pageInfo.total || 0} 条数据
        </span> */}
      </div>
      {/* <Button
        variant="outline"
        size="sm"
        className="border-line-3 flex h-7 items-center gap-1 rounded-[0.375rem] border"
        onClick={() => {
          // 导出报告
          if (!taskId || !classData.value.assignId) {
            toast.error("导出失败");
            return;
          }

          // 构建导出参数
          const exportParams = {
            taskId: taskId.value,
            assignId: classData.value.assignId,
            resourceId: currentCourse.value?.id,
            // 添加排序参数
            sortBy: sortBySignal.value,
            sortType: sortTypeSignal.value,
          };

          // 构建文件名：title+classname
          const fileName = `${classData.value.title || "作业报告"}_${classData.value.className || "班级报告"}`;

          // 调用导出服务
          exportReport(exportParams)
            .then((blob) => {
              const _blob = new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
              });
              // 下载文件
              downloadFile(_blob, `${fileName}.csv`);
              toast.success("导出成功，报告已下载到您的设备");
            })
            .catch((error) => {
              console.error("导出失败:", error);
              toast.error("导出失败");
            });
        }}
      >
        {ExportIconNew}
        <span className="text-xs">导出</span>
      </Button> */}
    </div>
  );

  // if (loading.value) {
  //   return <ReportPageSkeleton />;
  // }

  // 班级视图
  return (
    <ScrollArea className="h-full [&>.scroll-area-viewport>div:first-child]:w-full [&>.scroll-area-viewport>div:first-child]:table-fixed">
      <div className="rounded-lg px-6 pt-4">
        {/* 固定在顶部的内容 */}
        <div className="z-10">
          <div className="mb-5 flex gap-3">
            {/* 值得表扬区域 */}
            <StudentSection
              title="值得鼓励"
              students={praiseList.value || []}
              button={
                <ThumbsUpButton
                  className="border-line-1 bg-primary-6 text-primary-2 flex !h-[1.75rem] cursor-pointer items-center gap-1 rounded-[1.125rem] border pl-2 text-[10px] shadow-none hover:text-white active:text-white"
                  onClick={() => {
                    // DONE: 埋点18 => `homework_list_report_like` 作业报告页中点击了一键表扬
                    umeng.trackEvent(
                      UmengCategory.HOMEWORK,
                      UmengHomeworkAction.REPORT_LIKE,
                      {
                        subject: taskData.value?.subject,
                        job: getRoleSummary(),
                      }
                    );
                    handleRunPraise();
                  }}
                  disabled={praiseList.value?.length === 0}
                >
                  一键鼓励
                </ThumbsUpButton>
              }
              showButton={true} //{settingsValue.value.oneClickPraise} // 使用计算值
              onStudentClick={handleStudentClick}
              type="praise"
              setType={setTaskType}
              loading={loading.value}
            />

            {/* 需要关注区域 */}
            <StudentSection
              title="需要关注"
              classNames={{
                titlePrefix: "bg-orange-1",
              }}
              students={attentionList.value || []}
              showButton={settingsValue.value.oneClickReminder} // 使用计算值
              button={
                <Button
                  size="sm"
                  className="border-line-1 bg-primary-6 text-primary-2 flex !h-[1.75rem] cursor-pointer items-center gap-1 rounded-[1.125rem] border pl-2 text-[10px] shadow-none hover:text-white active:text-white"
                  onClick={() => {
                    // DONE: 埋点17 => `homework_list_report_push` 作业报告页中点击了一键提醒
                    umeng.trackEvent(
                      UmengCategory.HOMEWORK,
                      UmengHomeworkAction.REPORT_PUSH,
                      {
                        subject: taskData.value?.subject,
                        job: getRoleSummary(),
                      }
                    );
                    handleRunRemind();
                  }}
                  disabled={attentionList.value?.length === 0}
                >
                  {/* <Image
                    src={BellIcon.src}
                    alt="提醒"
                    className="h-4 w-4"
                    width={16}
                    height={16}
                  /> */}
                  {BellIconNew}
                  一键提醒
                </Button>
              }
              onStudentClick={handleStudentClick}
              type="attention"
              setType={setTaskType}
              loading={loading.value}
            />

            {/* 班级统计区域 */}
            <ClassStats
              averageAccuracy={classStat.value.averageAccuracy}
              classProgress={classStat.value.classProgress}
              loading={loading.value}
            />
          </div>

          {/* 使用 GliTable 展示数据，通过属性控制表头固定 */}
          <DataTable
            localKey="homework-report-table"
            height={"calc(100vh-19rem)"}
            data={filteredStudents.value}
            columns={columns}
            rowKey="studentId"
            toolbar={customToolbar}
            className="!h-[calc(100vh-14rem)] border-none bg-white"
            classNames={{
              container:
                "bg-white relative py-4 px-5 rounded-[0.75rem] border border-line-1",
              header: "bg-fill-gray-2", // 移除sticky相关样式，使用属性控制
              headerRow: "border-none ",
              row: "hover:bg-gray-50 transition-colors",
              empty: "text-gray-500 italic",
              loading: "text-primary animate-pulse",
              cell: "text-[0.875rem] text-gray-1 font-normal leading-[150%]",
            }}
            onSortingChange={(sortingState: SortingState) => {
              if (sortingState.length === 0) {
                // 没有排序时清空排序状态
                handleSortChange(undefined, undefined);
              } else {
                // 从排序信息中获取字段和方向
                const [{ id, desc }] = sortingState;
                handleSortChange(id, desc ? "desc" : "asc");
              }
            }}
            skeletonLoading={loading.value}
          />
        </div>

        {/* 学生详情抽屉 */}
        <StudentRemindDrawer
          open={drawerOpenSignal.value}
          onOpenChange={(open) => {
            drawerOpenSignal.value = open;
            // if (open) {
            //   cancel();
            // } else {
            //   run();
            // }
          }}
          studentId={selectedStudentIdSignal.value}
          student={selectedStudentSignal.value}
          onActionSuccess={() => fetchHomeworkDetail(true)}
          type={taskType}
        />
      </div>
    </ScrollArea>
  );
}

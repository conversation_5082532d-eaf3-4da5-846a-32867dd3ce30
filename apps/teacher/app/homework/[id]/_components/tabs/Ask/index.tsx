"use client";

import { useUmeng } from "@/hooks/useUmeng";
import IcComingSoon from "@/public/icons/ic_coming_soon.svg";
import { UmengCategory } from "@/utils";
import { useTaskContext } from "../../../_context/task-context";

export default function Results() {
  const { viewMode } = useTaskContext();
  useUmeng(
    UmengCategory.HOMEWORK,
    viewMode.value === "student"
      ? "homework_list_report_student_detail_ask_tab"
      : "homework_list_report_ask_tab"
  );
  return (
    <div className="align-center pt-50 flex flex-col justify-center p-4">
      <IcComingSoon width={120} height={120} className="w-30 h-30 m-auto" />
      <div className="text-gray-4 py-6 text-center text-sm">新功能敬请期待</div>

      {/* <NormalEmpty className="h-88.25" /> */}
    </div>
  );
}

"use client";

import { DataTable, TableColumn } from "@/app/homework/_components/DataTable";
import { ResourceReport } from "@/types/homeWork";
import { Sheet, SheetContent, SheetTitle } from "@/ui/sheet";
import { cn } from "@/utils/utils";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { X } from "lucide-react";
import { DrawerCard } from "../../../../../../components/common/drawer-card";
import {
  activeTabSignal,
  useTaskContext,
} from "../../../_context/task-context";
import { DrawerSkeleton } from "../../skeleton";
import { useAnswerResults } from "../../tabs/Results/store/answers";

import { Button } from "@/ui/tch-button";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";

interface DrawerContainerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export function DrawerContainer({
  open,
  onOpenChange,
  onConfirm,
}: DrawerContainerProps) {
  const {
    loading,
    currentCourse,
    homeworkData,
    classData,
    // useFetchHomeworkDetailRequest,
  } = useTaskContext();
  const { fetchParams } = useAnswerResults();
  const selectAllSignal = useSignal<boolean>(currentCourse.value === null);
  // 用于临时保存选择的课程，只有点击确定后才设置到 currentCourse
  const selectedCourseSignal = useSignal<{
    id: string;
    name: string;
    type: number;
  } | null>(currentCourse.value);
  // 选择的单选值，"all" 表示全选，其他为课程ID
  const selectedValueSignal = useSignal<string>(
    currentCourse.value === null ? "all" : currentCourse.value.id
  );

  const selectedCourseListSignal = useSignal<
    Array<{
      id: string;
      name: string;
      type: number;
    }>
  >([]);

  useSignalEffect(() => {
    if (activeTabSignal.value === "report") {
      classData.value.contentId =
        selectedValueSignal.value === "all"
          ? undefined
          : selectedValueSignal.value;
    } else if (activeTabSignal.value === "results") {
      fetchParams.value.resourceId =
        selectedValueSignal.value === "all"
          ? undefined
          : selectedValueSignal.value;
    }
  });

  // const { run, cancel } = useFetchHomeworkDetailRequest();

  // 课程列表数据处理，将resourceReports数据转换为课程列表
  const courseList = useComputed(() => {
    const reports = homeworkData.value?.detail?.resourceReports;

    return reports || [];
  });

  // 处理单选值变化
  const handleRadioChange = (value: string) => {
    selectedValueSignal.value = value;
    if (value === "all") {
      // 选择全部课程
      selectedCourseSignal.value = null;
    } else {
      // 选择特定课程
      const selectedCourse = courseList.value.find(
        (course) => course.resourceId === value
      );
      if (selectedCourse) {
        selectedCourseSignal.value = {
          id: selectedCourse.resourceId,
          name: selectedCourse.resourceName,
          type: selectedCourse.resourceType,
        };
      }
    }
  };

  // const handleCheckboxChange = (value: string | ResourceReport) => {
  //   if (typeof value === "string" && value === "all") {
  //     if (selectedCourseListSignal.value.length === courseList.value.length) {
  //       selectedCourseListSignal.value = [];
  //     } else {
  //       selectedCourseListSignal.value = courseList.value.map((course) => ({
  //         id: course.resourceId,
  //         name: course.resourceName,
  //         type: course.resourceType,
  //       }));
  //     }
  //   } else if (value) {
  //     const index = selectedCourseListSignal.value.findIndex(
  //       (course) => course.id === (value as ResourceReport).resourceId
  //     );
  //     if (index === -1) {
  //       selectedCourseListSignal.value.push({
  //         id: (value as ResourceReport).resourceId,
  //         name: (value as ResourceReport).resourceName,
  //         type: (value as ResourceReport).resourceType,
  //       });
  //     } else {
  //       selectedCourseListSignal.value.splice(index, 1);
  //     }
  //   }
  // };

  // 确认选择课程，更新到上下文并关闭抽屉
  const handleConfirm = () => {
    // DONE: 埋点14 => `homework_list_report_lesson_window_click` 作业报告页中在课节范围抽屉中切换了课节
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.REPORT_LESSON_WINDOW_CHANGE,
      {
        eventName: "作业报告页中在课节范围抽屉中切换了课节",
      }
    );

    // 更新全局状态
    currentCourse.value = selectedCourseSignal.value;
    selectAllSignal.value = selectedValueSignal.value === "all";

    // 关闭抽屉
    onOpenChange(false);
    onConfirm();
  };

  // 取消选择，重置临时状态并关闭抽屉
  const handleCancel = () => {
    onOpenChange(false);
  };

  // 初始化临时选择状态
  useComputed(() => {
    if (open) {
      selectedCourseSignal.value = currentCourse.value;
      selectedValueSignal.value =
        currentCourse.value === null ? "all" : currentCourse.value.id;
      selectedCourseListSignal.value = courseList.value.map((course) => ({
        id: course.resourceId,
        name: course.resourceName,
        type: course.resourceType,
      }));
    }
  });

  // 定义表格列
  const columns: TableColumn<ResourceReport>[] = [
    {
      title: "课程名称",
      field: "resourceName",
      width: 60,
      align: "center",
      render: (value) => (
        <div className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap text-left">
          {value}
        </div>
      ),
    },
    {
      title: "完成进度",
      field: "completionRate",
      align: "center",
      width: 60,
      // render: (value) => `${value}%`,
      render: (value) => (
        <div className="inline-flex items-center justify-center gap-2">
          <div className="bg-fill-gray-2 h-2 w-24 rounded-full">
            <div
              className={`bg-primary-2 h-2 rounded-full`}
              style={{ width: `${Math.ceil(value * 100)}%` }}
            />
          </div>
          <span className={"text-gray-2"}>{Math.ceil(value * 100)}%</span>
        </div>
      ),
    },
    {
      title: "平时用时",
      field: "averageCostTime",
      align: "center",
      width: 60,
      render: (value) => `${Math.round((value || 0) / 1000)}分钟`,
    },
    // {
    //   title: "待关注题目数",
    //   field: "needAttentionQuestionNum",
    //   align: "center",
    //   width: 60,
    // },
    {
      title: "正确率",
      field: "correctRate",
      align: "center",
      // sortable: true,
      width: 60,
      render: (value) => `${Math.ceil(value * 100)}%`,
    },
    // {
    //   title: "需关注人数",
    //   field: "needAttentionUserNum",
    //   align: "center",
    //   // sortable: true,
    //   width: 60,
    // },
  ];

  // 自定义工具栏
  const customToolbar = (
    <div className="flex w-full items-center justify-between gap-4">
      <div className="text-base font-medium text-[#202131]">
        选择需查阅的课程
      </div>
    </div>
  );

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTitle>{null}</SheetTitle>
      <SheetContent
        side="right"
        className={cn(
          "flex flex-col gap-0 overflow-y-auto bg-[#F4F7FE] p-0 px-6 sm:max-w-[72vw]"
        )}
        closeable={false}
      >
        <div className="flex items-center justify-between py-5">
          <div className="flex items-center gap-[0.625rem]">
            {/* <div
              className="flex cursor-pointer items-center justify-center"
              onClick={handleCancel}
            >
              <ChevronLeft className="h-5 w-5 text-[#444963]" />
            </div> */}
            <span className="text-lg font-medium text-[#444963]">学习情况</span>
          </div>
          <div
            className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full"
            onClick={handleCancel}
          >
            <X className="h-5 w-5" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto pb-[4.5rem]">
          {loading.value ? (
            <DrawerSkeleton />
          ) : (
            <>
              <DrawerCard className="border-line-1 w-full overflow-hidden border">
                <DataTable
                  localKey="courses-table"
                  data={courseList.value}
                  columns={columns}
                  rowKey="resourceId"
                  toolbar={customToolbar}
                  showColumnSetting={false}
                  className="w-full rounded-[0.25rem_0.25rem_0_0] border-none bg-white"
                  // selection={{
                  //   title: "选择课程",
                  //   // 当选择"全部课程"时，启用全选模式，所有行都显示为选中
                  //   // 当选择具体课程时，设置对应的 selectedKey
                  //   // selectedKey:
                  //   //   selectedValueSignal.value === "all"
                  //   //     ? undefined
                  //   //     : selectedValueSignal.value,
                  //   // selectAll: selectedValueSignal.value === "all",
                  //   selectedKeys: selectedCourseListSignal.value.map(
                  //     (course) => course.id
                  //   ),
                  //   onChange: (key, row) => {
                  //     // if (key) {
                  //     //   handleRadioChange(key.toString());
                  //     // }
                  //     // handleCheckboxChange(row as ResourceReport);
                  //     console.log(key, row);
                  //     handleCheckboxChange(
                  //       (row as any).original as ResourceReport
                  //     );
                  //   },
                  //   onIsSelected: (row) => {
                  //     console.log("isSelected", row);
                  //     return (
                  //       selectedCourseListSignal.value.findIndex(
                  //         (course) => course.id === (row as any).id
                  //       ) !== -1
                  //     );
                  //   },
                  //   showToolbar: false,
                  // }}
                  singleSelection={{
                    title: "选择课程",
                    // 当选择"全部课程"时，启用全选模式，所有行都显示为选中
                    // 当选择具体课程时，设置对应的 selectedKey
                    selectedKey:
                      selectedValueSignal.value === "all"
                        ? undefined
                        : selectedValueSignal.value,
                    selectAll: selectedValueSignal.value === "all",
                    onChange: (key) => {
                      if (key) {
                        handleRadioChange(key.toString());
                      }
                    },
                  }}
                  onRowClick={(record) => {
                    handleRadioChange(record.resourceId);
                  }}
                  classNames={{
                    container: "bg-white relative",
                    header: "!bg-white",
                    headerRow: "border-none text-gray-4 !bg-white",
                    headerCell: "text-gray-4 !font-normal bg-white text-xs",
                    cell: "text-sm py-3 text-sm",
                  }}
                />
              </DrawerCard>

              {/* 底部固定操作栏 */}
              <div className="absolute bottom-0 left-0 right-0 flex items-center justify-between border-t border-gray-200 bg-white px-6 py-4">
                <div className="flex items-center">
                  {/* <input
                    type="checkbox"
                    id="all-courses-footer"
                    checked={
                      selectedCourseListSignal.value.length ===
                      courseList.value.length
                    }
                    onChange={() => handleCheckboxChange("all")}
                    className="text-primary focus:ring-primary h-4 w-4 border-gray-300"
                  /> */}
                  <input
                    type="checkbox"
                    id="all-courses-footer"
                    checked={selectedValueSignal.value === "all"}
                    onChange={() => handleRadioChange("all")}
                    className="text-primary focus:ring-primary h-4 w-4 border-gray-300"
                  />
                  <label
                    htmlFor="all-courses-footer"
                    className="ml-2 cursor-pointer text-sm"
                  >
                    全部课程
                  </label>
                </div>

                <div className="flex items-center gap-3">
                  <Button
                    onClick={handleCancel}
                    className="flex h-[2.25rem] w-[7.5rem] items-center justify-center rounded-[1.125rem] border border-[#CFD5E8] bg-white px-5 py-2"
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleConfirm}
                    className="flex h-[2.25rem] w-[7.5rem] items-center justify-center rounded-[1.125rem] px-5 py-2 text-sm"
                  >
                    确定
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}

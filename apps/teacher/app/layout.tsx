import AppLayout from "@/app/_layout";
import AppSkeleton from "@/app/skeleton";
import { TEACHER_APP_LAYOUT_ID } from "@/configs";
import { cn } from "@/utils";
import type { Metadata, Viewport } from "next";
import localFont from "next/font/local";
import "./globals.css";

const teacherFont = localFont({
  src: [
    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-Regular/AlibabaPuHuiTi-3-55-Regular.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap",
  preload: true,
  fallback: ["teacherFontL3"],
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const teacherFontL3 = localFont({
  src: [
    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-Regular/AlibabaPuHuiTi-3-55-RegularL3.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap",
  preload: false,
});

export const metadata: Metadata = {
  title: "小鹿爱学",
  description: "小鹿爱学-教师端",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
  login,

  feedback,
}: Readonly<{
  children: React.ReactNode;
  feedback: React.ReactNode;
  login: boolean;
}>) {
  return (
    <html lang="zh-CN" className={cn("h-full", teacherFont.className)}>
      <head>
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_API_HOST} />
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_API_HOST} />
        <link rel="preconnect" href="https://static.xiaoluxue.com" />
        <link rel="dns-prefetch" href="https://static.xiaoluxue.com" />
        <link rel="prefetch" href={process.env.NEXT_PUBLIC_API_HOST} />
      </head>
      <body>
        <AppSkeleton />
        <AppLayout id={TEACHER_APP_LAYOUT_ID} login={login} feedback={feedback}>
          {children}
        </AppLayout>
      </body>
    </html>
  );
}

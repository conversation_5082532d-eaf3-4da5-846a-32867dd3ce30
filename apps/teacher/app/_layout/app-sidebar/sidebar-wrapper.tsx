"use client";
import { LOWER_MENU_ITEMS, UPPER_MENU_ITEMS } from "@/configs/layout";
import { useApp, useLogin } from "@/hooks";
import IcLogoIndex from "@/public/icons/logo_index.svg";
import { getUnreadMessageCount } from "@/services";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/ui/select";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/ui/sidebar";
import Avatar from "@/ui/tch-avatar";
import { useSignal } from "@preact-signals/safe-react";
import { openScheme } from "@repo/lib/utils/device";
import { useRequest } from "ahooks";
import { usePathname, useRouter } from "next/navigation";
import MenuLink from "./menu-link";
import Switch from "./switch";

function SidebarWrapper(props: React.ComponentProps<"div">) {
  const pathname = usePathname();
  const router = useRouter();
  const { userInfo, cleanupAppData, hasJobTypes } = useApp();
  const { setIsLogin } = useLogin();
  const sidebarFooter = useSignal("");
  const unreadMessageCount = useSignal<number>(0);

  useRequest(getUnreadMessageCount, {
    pollingInterval: 1000 * 60 * 60,
    onSuccess: (res) => {
      unreadMessageCount.value = res.unread_count;
    },
    onError: () => {
      unreadMessageCount.value = 0;
    },
  });

  return (
    <Sidebar {...props}>
      <SidebarHeader className="px-5">
        <div className="h-17.5 flex items-center">
          <IcLogoIndex className="ml-1.25 mr-2.25" />
          <div className="text-gray-1 text-lg font-medium">小鹿爱学</div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {UPPER_MENU_ITEMS.map((item) => {
                const roles = item.roles;

                if (roles && !hasJobTypes(roles as number[])) {
                  return null;
                }

                return (
                  <SidebarMenuItem key={item.title}>
                    <MenuLink
                      href={item.url}
                      icon={item.icon}
                      activeIcon={item.activeIcon}
                      active={
                        pathname.startsWith(item.url) ||
                        pathname === item.strictUrl
                      }
                    >
                      {item.title}
                    </MenuLink>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator className="my-2" />

        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {LOWER_MENU_ITEMS.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <MenuLink
                    href={item.url}
                    icon={item.icon}
                    activeIcon={item.activeIcon}
                    active={pathname.startsWith(item.url)}
                  >
                    {item.title}
                  </MenuLink>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <Select
          value={sidebarFooter.value}
          onValueChange={(value) => {
            if (value === "/logout") {
              cleanupAppData();
              setIsLogin(false);
              router.push("/login");

              return;
            }

            if (value === "/help") {
              openScheme({
                url: "https://wcng60ba718p.feishu.cn/docx/UY0XdThP2oBaiCxIiIrcHsH2nhb?from=from_copylink",
                title_bar: 0,
                full_screen: 0,
                status_bar_dark_font: true,
                // status_bar_color: "#F5FAFF",
              });

              return;
            }

            if (value === "/personal-center") {
              unreadMessageCount.value = 0;
            }

            router.push(value);
          }}
        >
          <SelectTrigger asChild>
            <div className="flex h-12 cursor-pointer items-center gap-2 pl-2 pr-2 active:opacity-80">
              <div className="border-1 relative box-content h-8 w-8 flex-none rounded-full border-white">
                <Avatar className="h-8 w-8" src={userInfo?.avatarUrl ?? ""} />
                {/* 未读消息标记 */}
                {unreadMessageCount.value > 0 && (
                  <span className="bg-danger-2 absolute right-0 top-0 h-2 w-2 rounded-full"></span>
                )}
              </div>

              <div className="flex-1 overflow-hidden">
                <p className="text-gray-2 truncate text-sm font-medium">
                  {userInfo?.userName}
                </p>
                <p className="text-xxs text-gray-4">
                  教师 ID: {userInfo?.userID}
                </p>
              </div>
            </div>
          </SelectTrigger>

          <SelectContent>
            <SelectItem value="/personal-center" size="default">
              <span className="relative">
                {/* 未读消息标记 */}
                {unreadMessageCount.value > 0 && (
                  <span className="bg-danger-2 absolute -right-0.5 top-0 h-2 w-2 rounded-full"></span>
                )}
                个人中心
              </span>
            </SelectItem>
            <SelectItem value="/feedback" size="default">
              用户反馈
            </SelectItem>
            <SelectItem value="/help" size="default">
              帮助手册
            </SelectItem>
            {/* <SelectItem value="/group-management" size="default">
              群组管理
            </SelectItem>
            <SelectItem value="/notice" size="default">
              公告
            </SelectItem> */}
            <SelectItem
              value="/logout"
              size="default"
              className="!text-danger-1"
            >
              退出登录
            </SelectItem>
          </SelectContent>
        </Select>
      </SidebarFooter>

      {/* 侧边栏开关 */}
      <Switch />
    </Sidebar>
  );
}

export default SidebarWrapper;

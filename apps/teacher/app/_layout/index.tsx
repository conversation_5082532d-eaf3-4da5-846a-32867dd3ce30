"use client";
import { LOCALSTORAGE_TOKEN_KEY } from "@/configs";
import { LoginContext } from "@/hooks";
import { ScrollArea } from "@/ui/scroll-area";
import dynamic from "next/dynamic";
import React from "react";
import store from "store2";
import { match } from "ts-pattern";
import AuthProvider from "./auth-provider";
import "./init-app";

const AppProvider = dynamic(
  () => import(/* webpackPreload: true */ "./app-provider"),
  {
    ssr: false,
  }
);

export default function AppLayout({
  children,
  id,
  login,
  feedback,
}: Readonly<{
  children: React.ReactNode;
  id: string;
  login: React.ReactNode;
  feedback: React.ReactNode;
}>) {
  const [isLogin, setIsLogin] = React.useState(
    Boolean(store(LOCALSTORAGE_TOKEN_KEY))
  );

  return (
    <LoginContext value={{ isLogin, setIsLogin }}>
      <AuthProvider>
        {match(isLogin)
          .with(true, () => (
            <ScrollArea
              className="h-screen [&>.scroll-area-viewport>div:first-child]:w-full [&>.scroll-area-viewport>div:first-child]:table-fixed"
              id={id}
            >
              <div className="min-h-150 h-screen select-none overflow-hidden">
                <AppProvider>
                  {children}
                  <div key="feedback">{feedback}</div>
                </AppProvider>
              </div>
            </ScrollArea>
          ))
          .otherwise(() => login)}
      </AuthProvider>
    </LoginContext>
  );
}

"use client";
import { useLogin } from "@/hooks";
import { useAppAuthEmitter } from "@/libs";
import { useMount } from "ahooks";
import to from "await-to-js";
import { useRouter } from "next/navigation";
import React, { memo } from "react";

function AuthProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  /**
   * 统一处理认证失败 / 权限不足的重定向 （目前用于监听、处理在 axios 响应拦截器中发送的事件）
   */
  useAppAuthEmitter();
  const { isLogin } = useLogin();
  const router = useRouter();

  /**
   * 是否正在检查登录状态
   */
  const [isChecking, setIsChecking] = React.useState(true);

  // 检查登录状态
  useMount(() => {
    const checkAuthAndRedirect = async () => {
      const pathname = window.location.pathname;
      const isLoginPage = pathname.startsWith("/login");

      if (isLogin && isLoginPage) {
        router.replace("/course");
        return;
      }

      if (!isLogin && !isLoginPage) {
        router.replace("/login");
        return;
      }
    };

    to(checkAuthAndRedirect());
    setIsChecking(false);
  });

  return isChecking ? null : children;
}

export default memo(AuthProvider);

import { JO<PERSON>_TYPE } from "@/enums";
import { useApp } from "@/hooks";
import IcBtnArrow from "@/public/icons/ic_btn_arrow.svg";
import IcCopy from "@/public/icons/ic_copy.svg";
import IcTeacherBg from "@/public/icons/ic_teacher_bg.svg";
import IcTeacherBg1 from "@/public/icons/ic_teacher_bg_1.svg";
import IcTeacherBg3 from "@/public/icons/ic_teacher_bg_3.svg";
import { School } from "@/types";
import { AlertDialog } from "@/ui/alertDialog";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/ui/select";
import Avatar from "@/ui/tch-avatar";
import { toast } from "@/ui/toast";
import { cn } from "@/utils/utils";
import { useSignal } from "@preact-signals/safe-react";
import copy from "copy-text-to-clipboard";
import { useRouter } from "next/navigation";
import { match } from "ts-pattern";

const bgMap = {
  1: IcTeacherBg1,
  3: IcTeacherBg3,
};

const TeacherInfo = () => {
  const { userInfo, cleanupAppData } = useApp();
  const router = useRouter();

  const currentSchool = userInfo?.schoolInfos.find(
    (school) => school.schoolID === userInfo.currentSchoolID
  );

  const selectedSchool = useSignal<School | null>(null);

  if (!userInfo || !currentSchool) {
    return null;
  }

  const handleSchoolChange = (value: string) => {
    if (value !== currentSchool.schoolID.toString()) {
      selectedSchool.value = userInfo.schoolInfos.find(
        (school) => school.schoolID.toString() === value
      )!;
    }
  };

  return (
    <>
      <div className="flex gap-4">
        {/* 教师基本信息卡片 */}
        <div className="bg-blue-5 w-[17.8125rem] rounded-[1.25rem] p-6">
          <div className="flex items-center gap-5">
            {/* 头像 */}
            <Avatar
              className="h-14 w-14 border-2 border-white shadow-lg"
              src={userInfo.avatarUrl}
            />

            <div className="text-gray-1 text-[1.375rem]">
              {userInfo.userName}
            </div>
          </div>

          <div className="my-5 border-t border-black/5" />

          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-5">
              <div className="text-gray-1 text-base font-medium opacity-70">
                手机号
              </div>
              <div className="text-base opacity-50">
                {userInfo.userPhone.slice(0, 3)}****
                {userInfo.userPhone.slice(-4)}
              </div>
            </div>

            <div className="flex items-center gap-5">
              <div className="text-gray-1 text-base font-medium opacity-70">
                教师ID
              </div>
              <div className="flex items-center gap-0.5">
                <div className="text-base opacity-50">{userInfo.userID}</div>
                <IcCopy
                  className="size-3.5 cursor-copy"
                  onClick={() => {
                    const ret = copy(userInfo.userID.toString());

                    if (ret) {
                      toast.success(`复制成功`);
                    } else {
                      toast.error(`复制失败`);
                    }
                  }}
                ></IcCopy>
              </div>
            </div>
          </div>
        </div>

        {/* 当前职务卡片 */}
        <div className="bg-blue-5 flex-1 rounded-[1.25rem] p-6">
          <div className="flex items-center gap-3 text-base">
            <div className="text-gray-1 flex-none text-base font-medium">
              当前职务
            </div>

            {userInfo.schoolInfos.length === 1 ? (
              <div className="text-gray-1 text-base opacity-50">
                {currentSchool?.schoolName}
              </div>
            ) : (
              <Select
                value={currentSchool.schoolID.toString()}
                onValueChange={handleSchoolChange}
              >
                <SelectTrigger
                  asChild
                  className="flex cursor-pointer items-center gap-1 opacity-50"
                >
                  <div>
                    {currentSchool?.schoolName}
                    <IcBtnArrow className="h-3 w-3 flex-none" />
                  </div>
                </SelectTrigger>

                <SelectContent>
                  {userInfo!.schoolInfos.map((school) => (
                    <SelectItem
                      key={school.schoolID}
                      value={school.schoolID.toString()}
                    >
                      {school.schoolName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            <AlertDialog
              className="w-[32.5rem]"
              open={Boolean(selectedSchool.value)}
              title={`是否切换至${selectedSchool.value?.schoolName}学校？`}
              description="您需要退出当前学校，重新登录"
              onCancel={() => {
                selectedSchool.value = null;
              }}
              onOk={async () => {
                cleanupAppData();

                router.push(
                  `/login?schoolId=${selectedSchool.value?.schoolID}`
                );
                return true;
              }}
            />
          </div>

          <div className={cn(`mt-5 space-y-4`)}>
            {userInfo.teacherJobInfos.map((teacherJobInfos, index) => {
              const BgIcon =
                bgMap[teacherJobInfos.jobType?.jobType as keyof typeof bgMap] ??
                IcTeacherBg;
              return (
                <div
                  key={index}
                  className="relative isolate flex-1 overflow-hidden rounded-2xl bg-white p-6 shadow-[0_0.25rem_1.5rem_rgba(16,18,25,0.03)]"
                >
                  <div className="text-base font-medium leading-normal">
                    {teacherJobInfos.jobType?.name}
                  </div>

                  <div className="text-gray-4 mt-1 text-xs leading-normal">
                    {teacherJobInfos.jobSubject?.name &&
                      `${teacherJobInfos.jobSubject.name} / `}

                    {match(teacherJobInfos.jobType?.jobType)
                      .with(JOB_TYPE.JOB_TYPE_SUBJECT_TEACHER, () => {
                        return (
                          teacherJobInfos.jobInfos
                            ?.map((info) => {
                              const name = info.name;

                              if (!info.jobClass) {
                                return name;
                              }

                              return info.jobClass
                                .map((item) => {
                                  return `${name}(${item.name})`;
                                })
                                .join(" / ");
                            })
                            ?.join(" / ") ?? ""
                        );
                      })
                      .otherwise(() =>
                        teacherJobInfos.jobInfos
                          ?.map((info) => info.name)
                          .join(" / ")
                      )}
                  </div>

                  <div className="-z-1 absolute bottom-0 right-0">
                    <BgIcon className="size-25" />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};

export default TeacherInfo;

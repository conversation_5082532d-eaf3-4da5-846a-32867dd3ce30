import { Skeleton } from "@/ui/skeleton";
import React from "react";

export const AppSkeleton: React.FC = () => {
  return (
    <div className="-z-1 bg-fill-light fixed left-0 top-0 flex h-full w-full overflow-hidden">
      {/* 左侧边栏骨架 */}
      <div className="w-42 flex h-full flex-col justify-between bg-[#EBF1FF] p-5 pb-4 pt-5">
        {/* Logo 区域 */}
        <div className="flex flex-col gap-8">
          <Skeleton className="h-9 w-28 bg-[#DFE5F8]" />

          {/* 菜单项骨架 */}
          <div className="flex flex-col gap-8">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3">
                <Skeleton className="h-6 w-6 rounded-full bg-[#DFE5F8]" />
                <Skeleton className="h-5 flex-1 bg-[#DFE5F8]" />
              </div>
            ))}
          </div>
        </div>

        {/* 底部用户信息骨架 */}
        <div className="flex items-center gap-3">
          <Skeleton className="h-9 w-9 rounded-full bg-[#DFE5F8]" />
          <div className="flex flex-col gap-1">
            <Skeleton className="w-18.5 h-3.5 bg-[#DFE5F8]" />
            <Skeleton className="h-3.5 w-12 bg-[#DFE5F8]" />
          </div>
        </div>
      </div>

      {/* 右侧主内容区域骨架 */}
      <div className="flex h-full flex-1 flex-col">
        {/* 主内容区 */}
        <div className="flex flex-1 flex-col gap-8 p-6 pt-5">
          {/* 顶部区域 */}
          <div className="flex flex-col gap-5">
            {/* 标题 */}
            <div className="flex w-fit items-center gap-1.5">
              <Skeleton className="h-7 w-12" />
            </div>

            {/* 功能卡片 */}
            <div className="flex gap-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton key={index} className="h-24 flex-1" />
              ))}
            </div>
          </div>

          {/* 最近布置区域 */}
          <div className="flex flex-col gap-5">
            {/* 标题 */}
            <Skeleton className="h-7 w-20" />

            {/* 内容卡片网格 */}
            <div className="grid grid-cols-4 gap-4">
              {/* 第一行 - 大卡片 */}
              <Skeleton className="col-span-1 h-6" />
              <Skeleton className="col-span-1 h-6" />
              <Skeleton className="col-span-1 h-6" />
              <Skeleton className="col-span-1 h-6" />

              {/* 第二行 - 小标签 */}
              <Skeleton className="w-19.5 col-span-1 h-4" />
              <Skeleton className="w-19.5 col-span-1 h-4" />
              <Skeleton className="w-19.5 col-span-1 h-4" />
              <Skeleton className="w-19.5 col-span-1 h-4" />

              {/* 第三行 - 内容卡片 */}
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={`row3-${index}`}
                  className="h-25.75 col-span-1"
                />
              ))}

              {/* 第四行 - 内容卡片 */}
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={`row4-${index}`}
                  className="h-25.75 col-span-1"
                />
              ))}

              {/* 第五行 - 内容卡片 */}
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={`row5-${index}`}
                  className="h-25.75 col-span-1"
                />
              ))}

              {/* 第六行 - 内容卡片 */}
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={`row6-${index}`}
                  className="h-25.75 col-span-1"
                />
              ))}

              {/* 第七行 - 内容卡片 */}
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={`row7-${index}`}
                  className="h-25.75 col-span-1"
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppSkeleton;

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { LOCALSTORAGE_TOKEN_KEY } from '@/configs/storage';

export async function POST(request: NextRequest) {
  const body = await request.json();
  const { token } = body;

  if (!token || typeof token !== 'string') {
    return new NextResponse(
      JSON.stringify({ success: false, error: 'Invalid token' }),
      { status: 400 }
    );
  }

  const response = NextResponse.json({ success: true, token }, { status: 200 });

  response.cookies.set({
    name: LOCALSTORAGE_TOKEN_KEY,
    value: token,
    httpOnly: false,
    path: '/',
    expires: 500 * 60 * 60 * 1000,
  });

  return response;
}

export async function DELETE() {
  const response = NextResponse.json({ success: true }, { status: 200 });
  response.cookies.delete(LOCALSTORAGE_TOKEN_KEY);
  return response;
}
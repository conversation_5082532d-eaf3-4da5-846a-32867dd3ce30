"use client";
import { FEEDBACK_MAX_IMAGES } from "@/configs";
import { FEEDBACK_TYPE } from "@/enums";
import { useFeedback } from "@/hooks";
import CloseIcon from "@/public/icons/ic_close.svg";
import { getFeedbackTypeConfig } from "@/services";
import { ScrollArea } from "@/ui/scroll-area";
import { Button } from "@/ui/tch-button";
import { TchSheet } from "@/ui/tch-sheet";
import { Textarea } from "@/ui/textarea";
import { toast } from "@/ui/toast";
import { cn } from "@/utils";
import { useRequest } from "ahooks";
import { notFound, useParams, useRouter } from "next/navigation";
import { useState } from "react";
import FeedbackImageUpload from "../_components/FeedbackImageUpload";

export default function FeedbackTypePage() {
  const router = useRouter();
  // 反馈类型
  const { feedbackType } = useParams<{ feedbackType: FEEDBACK_TYPE }>();

  if (!Object.values(FEEDBACK_TYPE).includes(feedbackType as FEEDBACK_TYPE)) {
    notFound();
  }

  const { submitRequest, open, setOpen } = useFeedback();

  // 获取反馈类型配置信息
  const { data: feedbackTypeConfig } = useRequest(
    () => getFeedbackTypeConfig(feedbackType),
    {
      cacheKey: `getFeedbackTypeConfig-${feedbackType}`,
    }
  );

  const [feedbackSubTypes, setFeedbackSubTypes] = useState<string[]>([]);

  const handleTypeClick = (t: string) => {
    setFeedbackSubTypes((prev) =>
      prev.includes(t) ? prev.filter((x) => x !== t) : [...prev, t]
    );
  };

  const [description, setDescription] = useState("");

  const [images, setImages] = useState<File[]>([]);

  // 提交
  const handleSubmit = async () => {
    if (submitRequest.loading) return;

    if (feedbackSubTypes.length === 0) {
      toast.warning("请选择问题类型");
      return;
    }

    await submitRequest.runAsync({
      description,
      images,
      feedbackType,
      feedbackSubType: feedbackSubTypes,
    });

    toast.success("反馈提交成功");
    setOpen(false);
  };

  return (
    <TchSheet
      className="w-130 max-w-130! outline-0"
      open={open}
      onAnimationEnd={() => {
        if (!open) {
          router.back();
        }
      }}
    >
      <div className="feedback_type_container bg-fill-light flex h-full flex-col">
        {/* Header */}
        <div className="feedback_type_header h-18 flex items-center justify-between px-6">
          <div className="text-gray-1 leading-7.5 text-xl font-semibold">
            问题反馈
          </div>
          <CloseIcon
            onClick={() => {
              setOpen(false);
            }}
            className="size-5 cursor-pointer active:opacity-40"
            width={20}
            height={20}
          />
        </div>

        <ScrollArea className="flex-1 overflow-hidden">
          <div className="min-h-132">
            <div className="space-y-4 px-6">
              {/* 类型选择 */}
              <div className="feedback_type_select border-line-1 pb-7.5 rounded-2xl border bg-white px-5 pt-5">
                <div className="text-gray-1 mb-5 text-base font-semibold">
                  <span className="text-danger-1">*</span>{" "}
                  请选择问题类型（可多选）
                </div>

                <div className="grid grid-cols-2 gap-x-3 gap-y-4">
                  {(feedbackTypeConfig ?? []).map((t) => (
                    <button
                      key={t.errorType}
                      type="button"
                      className={cn(
                        "feedback_type_btn text-gray-2 h-9 cursor-pointer rounded-md border text-sm outline-0",
                        feedbackSubTypes.includes(t.errorType) &&
                          "border-primary-2 bg-primary-2 text-white"
                      )}
                      onClick={() => handleTypeClick(t.errorType)}
                    >
                      {t.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* 截图上传区 */}
              <div className="feedback_type_upload border-line-1 pb-7.5 rounded-2xl border bg-white px-5 pt-5">
                <div className="text-gray-1 mb-5 text-base font-semibold">
                  上传截图：（{images.length}/{FEEDBACK_MAX_IMAGES}）
                </div>

                <FeedbackImageUpload images={images} onChange={setImages} />
              </div>

              {/* 问题描述区 */}
              <div className="feedback_type_desc border-line-1 pb-7.5 rounded-2xl border bg-white px-5 pt-5">
                <div className="text-gray-1 mb-5 text-base font-semibold">
                  请描述问题
                </div>
                <Textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="请输入文字反馈"
                  maxLength={300}
                  className={cn("text-gray-2 placeholder:text-gray-4")}
                />
              </div>
            </div>

            {/* 底部按钮区 */}
            <div className="feedback_type_footer bg-fill-white flex items-center justify-end px-6 py-4">
              <Button
                type="primary"
                radius="full"
                className={cn(`h-9 w-full text-sm font-medium`)}
                onClick={handleSubmit}
              >
                提交反馈
              </Button>
            </div>
          </div>
        </ScrollArea>
      </div>
    </TchSheet>
  );
}

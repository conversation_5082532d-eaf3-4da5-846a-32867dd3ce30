"use client";

import { FEEDBACK_TYPE } from "@/enums";
import { FeedbackSource, useFeedbackByType } from "@/hooks/useReportFeedback";
import { Button } from "@/ui/tch-button";
import { CourseView } from "@repo/core/components/ai-course-preview";
import { CircleMinusIcon, CirclePlusIcon } from "lucide-react";
import { memo, useCallback, useRef } from "react";
import { AssignCard } from "../assign-card";
import { AssignHeader } from "../assign-header";
import { AssignPageContainer } from "../assign-page-container";
import { useCoursePreviewModel } from "./model";
import WidgetReportButton from "./WidgetReportButton";

const FEEDBACK_TYPE_MAP = {
  guide: FEEDBACK_TYPE.COURSE,
  interactive: FEEDBACK_TYPE.COURSE,
  exercise: FEEDBACK_TYPE.QUESTION,
  video: FEEDBACK_TYPE.VIDEO,
};

interface AssignCoursePreviewProps {
  onBack?: () => void;
}

function AssignCoursePreview({ onBack }: AssignCoursePreviewProps) {
  const {
    detail,
    hasJoined,
    title,
    onRemoveCourse,
    onAddCourse,
    activeIndex,
    widgetData,
    isWidgetDataLoading,
    handleActiveIndexChange: _handleActiveIndexChange,
  } = useCoursePreviewModel({ onBack });
  const containerRef = useRef<HTMLDivElement>(null);

  const { routeToFeedback } = useFeedbackByType();

  // 上报需要的，暂时不会上移
  const questionIndex = useRef<number>(0);
  const handleQuestionIndexChange = useCallback((index: number) => {
    questionIndex.current = index;
  }, []);

  const handleActiveIndexChange = useCallback(
    (index: number) => {
      _handleActiveIndexChange(index);
      questionIndex.current = 0;
    },
    [_handleActiveIndexChange]
  );

  // TODO: 把model抽成context，然后这里直接调用context里的方法
  const reportWidget = useCallback(() => {
    const activeWidgetInfo = widgetData;
    if (!detail.value || !activeWidgetInfo) return;
    const type =
      FEEDBACK_TYPE_MAP[activeWidgetInfo.type] || FEEDBACK_TYPE.COURSE;

    const commonParams = {
      feedbackSource: FeedbackSource.COURSE_PREVIEW,
      subjectId: detail.value.subject ?? -1,
      feedbackPhaseId: detail.value.phase ?? -1,
      courseId: detail.value.lessonId,
      version: detail.value.publishVersion ?? "",
      widgetIndex: activeWidgetInfo.index ?? -1,
      feedbackWidgetName: activeWidgetInfo.name ?? "",
    };

    if (type === FEEDBACK_TYPE.QUESTION) {
      const question =
        activeWidgetInfo.data[questionIndex.current]?.questionInfo;

      return routeToFeedback(FEEDBACK_TYPE.QUESTION, {
        ...commonParams,
        questionId: question.questionId,
        feedbackQuestionVersionId: question.questionVersionId,
      });
    }
    if (type === FEEDBACK_TYPE.VIDEO) {
      return routeToFeedback(FEEDBACK_TYPE.VIDEO, {
        ...commonParams,
        // url: activeWidgetInfo.data.url,
      });
    }
    return routeToFeedback(FEEDBACK_TYPE.COURSE, {
      ...commonParams,
    });
  }, [detail.value, routeToFeedback, widgetData]);

  return (
    <AssignPageContainer>
      <div className="flex-0 h-17.5 flex items-center justify-between pr-6">
        <AssignHeader
          className="truncate"
          title={title.value}
          onBack={onBack}
        />

        {hasJoined.value ? (
          <Button
            type="error"
            size="lg"
            radius="full"
            className="px-4"
            onClick={onRemoveCourse}
          >
            <CircleMinusIcon size={20} className="mr-1.5 size-5" />
            <span>取消加入</span>
          </Button>
        ) : (
          <Button
            type="primary"
            size="lg"
            radius="full"
            className="px-4"
            onClick={onAddCourse}
          >
            <CirclePlusIcon size={20} className="mr-1.5 size-5" />
            <span>加入任务</span>
          </Button>
        )}
      </div>
      <div
        className="h-full w-full flex-1 items-start gap-2.5 overflow-hidden pb-4 pl-6 pr-4"
        ref={containerRef}
      >
        <AssignCard className="h-full w-full flex-1 overflow-hidden px-5 py-4">
          <div className="flex h-full flex-1 flex-col overflow-hidden">
            <div className="text-gray-1 text-base font-semibold leading-6">
              课堂讲解
            </div>
            <div className="border-1 border-primary-5 mb-3.5 mt-4 w-full"></div>

            <div className="relative flex flex-1 overflow-hidden">
              {detail.value?.lessonId && (
                <CourseView
                  className="flex-1"
                  progressData={detail.value?.lessonShowInfo.widgetList.map(
                    (e) => {
                      return {
                        index: e.widgetIndex,
                        type: e.widgetType,
                        name: e.widgetName,
                        data: e.data,
                        // NOTE：这个hidden后端没返回，先写死
                        hidden: 0,
                      };
                    }
                  )}
                  activeIndex={activeIndex.value}
                  widgetData={widgetData}
                  isWidgetDataLoading={isWidgetDataLoading}
                  onActiveIndexChange={handleActiveIndexChange}
                  onQuestionIndexChange={handleQuestionIndexChange}
                  // 问题反馈
                  WidgetLoaderSuffixElement={
                    <WidgetReportButton onReport={reportWidget} />
                  }
                  // 禁用点击播放
                  guideWidgetProps={{
                    clickToPlay: false,
                  }}
                />
              )}
            </div>
          </div>
        </AssignCard>
      </div>
    </AssignPageContainer>
  );
}

export default memo(AssignCoursePreview);

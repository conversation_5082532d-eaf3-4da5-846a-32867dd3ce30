"use client";

import {
  useAssignCourseContext
} from "@/app/assign/[subjectKey]/course/store";
import { getQuestionListByIds } from "@/services/assign-homework";
import { getAiCourseDetail } from "@/services/preview";
import { AiCourseDetail } from "@/types/assign";
import {
  minutesToString
} from "@/utils/date";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { CourseWidgetDetail } from "@repo/core/components/ai-course-preview/type";
import { useRequest } from "ahooks";

export type CoursePreviewTag = "video" | "";

export function useCoursePreviewModel({ onBack }: { onBack?: () => void }) {
  const {
    aiCoursePreviewData,
    selectedAiCourses,
    selectAiCourse,
    unselectAiCourse,

  } = useAssignCourseContext()


  // data
  // 课程id
  // 课程详情 - 知识图谱数据、视频讲解数据、题铺数据、笔记数据
  // TODO: 这个Detail变成useRequest的值
  const detail = useSignal<AiCourseDetail | null>(null);

  // computed
  // 是否已加入
  const hasJoined = useComputed(() => {
    return selectedAiCourses.value.some((item) => item.aiCourse.id === aiCoursePreviewData.value?.aiCourse.id);
  });

  // 标题
  const title = useComputed(() => {
    if (!detail.value) {
      return "";
    }
    const { lessonName, lessonShowInfo, bizTreeNodeList } = detail.value;
    const time = minutesToString(
      lessonShowInfo.widgetList.reduce<number>(
        (acc, curr) => acc + (curr.duration || 0),
        0
      ) / 60
    );
    return `课程学习：${bizTreeNodeList[0]?.bizTreeNodeName ?? lessonName}${time ? `（约${time}）` : ""}`;
  });

  useRequest(
    async () => {
      const bizTreeNodeId = aiCoursePreviewData.peek()?.bizTreeNodeId;
      if (!bizTreeNodeId) return;
      try {
        const res = await getAiCourseDetail(bizTreeNodeId);
        detail.value = res;
        return res ? res : null;
      } catch (err) {
        console.error(err);
        onBack?.();
        return null;
      }
    },
    {
      ready: aiCoursePreviewData.peek()?.bizTreeNodeId !== undefined,
    }
  );

  // method
  // TODO: 这3个B方法放到上层Model去
  // 1.加入列表
  const onAddCourse = () => {
    if (!aiCoursePreviewData.value) {
      return;
    }
    selectAiCourse(aiCoursePreviewData.value);
  };

  // 2.移出列表
  const onRemoveCourse = () => {
    if (!aiCoursePreviewData.value) {
      return;
    }
    unselectAiCourse(aiCoursePreviewData.value);
  };

  const fetchQuestionList = useRequest(
    async (ids: string[]) => {
      return await getQuestionListByIds({ questionIds: ids });
    },
    {
      manual: true,
    }
  );

  // 展示区数据和handler
  const activeIndex = useSignal(0);

  // 获取详情
  const { data: widgetData, loading: isWidgetDataLoading } = useRequest<
    CourseWidgetDetail | undefined,
    []
  >(
    async () => {
      if (!detail.value) {
        return undefined;
      }

      const index = activeIndex.value;

      const widgetInfo = detail.value.lessonShowInfo.widgetList[index];

      // 练习题，后端返回的是练习题id列表，需要查题库
      if (widgetInfo.widgetType === "exercise") {
        const ids =
          widgetInfo.data?.reduce<string[]>(
            (res, e) => res.concat(e.exerciseIds || []),
            []
          ) || [];
        if (ids.length === 0) {
          return undefined;
        }

        const res = await fetchQuestionList.runAsync(ids);

        return {
          index: widgetInfo.widgetIndex,
          name: widgetInfo.widgetName,
          duration: widgetInfo.duration || 0,
          type: "exercise",
          data: res.map((e, index) => {
            return {
              questionInfo: {
                ...e,
                questionIndex: index, // 题目序号不能为负，可能为 null
                questionDifficulty: e.questionDifficult, // 难度系数不能为负，可能为 null
                hasNextQuestion: res[index + 1] ? true : null, // 是否有下一题可能为 null
                isResume: false, // 是否为恢复练习状态
              },
            };
          }),
        };
      }

      return {
        index: widgetInfo.widgetIndex,
        name: widgetInfo.widgetName,
        type: widgetInfo.widgetType,
        data: widgetInfo.data,
        duration: widgetInfo.duration || 0,
      };
    },
    {
      ready: detail.value !== undefined,
      refreshDeps: [detail.value, activeIndex.value],
      cacheKey: `fetchWidgetDetail-${detail.value?.lessonId || -1}-${activeIndex.value}`,
    }
  );

  const handleActiveIndexChange = (index: number) => {
    activeIndex.value = index;
  };

  return {
    // 头部数据和handler
    detail,
    hasJoined,
    title,
    onAddCourse,
    onRemoveCourse,

    // 展示区数据和handler
    activeIndex,
    widgetData,
    isWidgetDataLoading,
    handleActiveIndexChange,
  };
}

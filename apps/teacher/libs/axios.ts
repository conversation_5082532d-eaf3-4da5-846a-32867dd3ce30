import { LOCALSTORAGE_SCHOOL_ID_KEY, LOCALSTORAGE_TOKEN_KEY } from "@/configs";
import { AppEvents, emitter } from "@/libs";
import type { GilAxiosInstance } from "@/types/axios";
import { toast } from "@/ui/toast";
import axios from "axios";
import store from "store2";

console.log("NEXT_PUBLIC_API_HOST ==>", process.env.NEXT_PUBLIC_API_HOST);
console.log(
  "NEXT_PUBLIC_UCENTER_API_URL ==> ",
  process.env.NEXT_PUBLIC_UCENTER_API_URL
);
const NEXT_PUBLIC_API_HOST = process.env.NEXT_PUBLIC_API_HOST || "";
const NEXT_PUBLIC_UCENTER_API_URL =
  process.env.NEXT_PUBLIC_UCENTER_API_URL || "";

const r: GilAxiosInstance = axios.create({
  baseURL: `${NEXT_PUBLIC_API_HOST}/teacher-api/api/v1`,
  timeout: 10000,
});

r.interceptors.request.use((config) => {
  const token = store(LOCALSTORAGE_TOKEN_KEY);
  const schoolId = store(LOCALSTORAGE_SCHOOL_ID_KEY);

  if (
    token &&
    NEXT_PUBLIC_UCENTER_API_URL &&
    !config.url?.startsWith(NEXT_PUBLIC_UCENTER_API_URL)
  ) {
    config.headers.Authorization = `Bearer ${token}`;
    config.headers.organizationId = schoolId;
  }

  return config;
});

r.interceptors.response.use(
  (response) => {
    if (response.config.responseType === "blob") {
      return response.data;
    }
    const {
      data: { code, message, data },
    } = response;

    if (code) {
      if ((response.config as { showToast?: boolean }).showToast !== false) {
        toast.warning(message);
      }
      throw response;
    }

    return data;
  },
  (err) => {
    const status = err?.response?.status;

    if (status === 401 || status === 403) {
      // 使用新的 emitter 实例和静态常量
      emitter.emit(AppEvents.APP_AUTH, { status });

      throw err;
    }

    const message = err?.response?.data?.message ?? "网络繁忙，请稍候再试！！";
    toast.warning(message);

    throw err;
  }
);

export { r };


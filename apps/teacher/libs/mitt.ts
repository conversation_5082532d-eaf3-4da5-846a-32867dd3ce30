"use client";
import { useLogin } from "@/hooks";
import { toast } from "@/ui/toast";
import { useMount } from "ahooks";
import mitt from "mitt";
import { useRouter } from "next/navigation";
import store from "store2";

/**
 * 事件总线实例
 */
export const emitter = mitt<EmitterEvents>();

/**
 * 一个封装了 emitter.on/off 的自定义 Hook，用于简化事件监听，在组件卸载时自动取消订阅。
 *
 * @param eventName 要监听的事件名称
 * @param handler 事件处理函数
 */
export function useEmitter<EventName extends keyof EmitterEvents>(
  eventName: EventName,
  handler: (event: EmitterEvents[EventName]) => void
) {
  useMount(() => {
    emitter.on(eventName, handler);

    return () => {
      emitter.off(eventName, handler);
    };
  });
}

/**
 * 监听 `AppEvents.APP_AUTH` 事件，并根据状态码重定向到登录页或 403 页面。
 */
export const useAppAuthEmitter = () => {
  const router = useRouter();
  const { setIsLogin } = useLogin()

  useEmitter(AppEvents.APP_AUTH, (event: EmitterEvents[typeof AppEvents.APP_AUTH]) => {
    if (event.status === 401) {
      toast.warning("登录已过期，请重新登录");
      store.clearAll();
      setIsLogin(false);
      router.replace("/login");
    } 
    
    if (event.status === 403) {
      router.replace("/403");
    }
  });
}; 

/**
 * app级别事件
 */
export const AppEvents = {
  APP_AUTH: "app-auth",
} as const;

export type EmitterEvents = {
  [AppEvents.APP_AUTH]: { status: number };
};